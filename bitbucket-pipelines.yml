image: node:16

definitions:
  caches:
    node: ~/.npm

  steps:
    - step: &deploy-to-s3
        name: Deploy to S3
        oidc: true
        caches:
          - node
        script:
          - echo "Installing dependencies..."
          - npm install
          - |
            echo "Checking branch information..."
            if [ -z "$BITBUCKET_BRANCH" ]; then
              export CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
            else
              export CURRENT_BRANCH="$BITBUCKET_BRANCH"
            fi
            echo "Branch detected: $CURRENT_BRANCH"
            echo "Deploying to S3 based on branch..."
            if [ "$CURRENT_BRANCH" = "develop" ]; then
              export S3_BUCKET="halo-ds-an2-s3-hub-luna"
              export AWS_OIDC_ROLE_ARN="arn:aws:iam::484907483548:role/halo-dc-role-bitbucketpipelineoidc"
              npm run build -- --mode development
            elif [ "$CURRENT_BRANCH" = "stage" ]; then
              export S3_BUCKET="halo-s-an2-s3-hub-luna"
              export AWS_OIDC_ROLE_ARN="arn:aws:iam::445567098182:role/halo-p-role-bitbucketpipelineoidc"
              npm run build -- --mode stage
            elif [ "$CURRENT_BRANCH" = "release" ]; then
              export S3_BUCKET="halo-dt-an2-s3-hub-luna"
              export AWS_OIDC_ROLE_ARN="arn:aws:iam::484907483548:role/halo-dc-role-bitbucketpipelineoidc"
              npm run build -- --mode sandbox
            elif [ "$CURRENT_BRANCH" = "main" ]; then
              export S3_BUCKET="halo-p-an2-s3-hub-luna"
              export AWS_OIDC_ROLE_ARN="arn:aws:iam::445567098182:role/halo-p-role-bitbucketpipelineoidc"
              npm run build -- --mode production
            else
              echo "Unsupported branch: $CURRENT_BRANCH"
              exit 1
            fi
            echo "S3_BUCKET is set to: $S3_BUCKET"
            echo "AWS_OIDC_ROLE_ARN is set to: $AWS_OIDC_ROLE_ARN"
          - pipe: atlassian/aws-s3-deploy:1.6.1
            variables:
              AWS_DEFAULT_REGION: ap-northeast-2
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              S3_BUCKET: $S3_BUCKET
              LOCAL_PATH: dist/
              ACL: private

pipelines:
  custom:
    deploy-to-s3:
      - step: *deploy-to-s3
  branches:
    develop:
      - step: *deploy-to-s3
    stage:
      - step: *deploy-to-s3
    release:
      - step: *deploy-to-s3
    main:
      - step: *deploy-to-s3
