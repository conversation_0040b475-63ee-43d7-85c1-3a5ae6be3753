import { defineConfig, loadEnv } from 'vite';
import { svelte } from '@sveltejs/vite-plugin-svelte';
import { svelteSVG } from 'rollup-plugin-svelte-svg';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import path from 'path'
// https://vitejs.dev/config/
export default ({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };

  return defineConfig({
    build: {
      sourcemap: true
    },
    plugins: [
      svelte(),
      svelteSVG({
        svgo: {},
        enforce: 'pre'
      }),
      sentryVitePlugin({
        org: 'tidesquare-aggregatortf',
        project: 'luna-web-svelte',
        authToken: process.env.SENTRY_AUTH_TOKEN
      })
    ],
    resolve: {
      alias: {
        src: path.resolve('./src'),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
          @import "./src/styles/_common.scss";
        `
        }
      }
    }
  });
}