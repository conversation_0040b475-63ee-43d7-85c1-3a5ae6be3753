{"name": "dashboard-svelte", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "vite build", "build:sandbox": "vite build --mode sandbox", "build:production": "vite build --mode production", "preview": "vite preview", "format": "prettier --write '{public,src}/**/*.{css,html,js,svelte}'"}, "devDependencies": {"@sentry/vite-plugin": "^2.6.1", "@sveltejs/vite-plugin-svelte": "^2.0.2", "@tsconfig/svelte": "^4.0.1", "path": "^0.12.7", "prettier": "^2.2.1", "prettier-plugin-svelte": "^2.2.0", "sass": "1.62.1", "svelte": "^4.0.0", "svooltip": "^0.7.8", "vite": "^4.1.0"}, "dependencies": {"@popperjs/core": "^2.11.8", "@sentry/svelte": "^7.48.0", "axios": "^1.4.0", "carbon-components-svelte": "^0.73.1", "carbon-icons-svelte": "^11.4.0", "lodash": "^4.17.21", "qs": "^6.11.2", "rollup-plugin-svelte-svg": "^1.0.0-beta.6", "svelte-i18n": "^3.6.0", "svelte-spa-router": "^3.3.0", "uuid": "^9.0.0"}}