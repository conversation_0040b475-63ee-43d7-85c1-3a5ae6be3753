<script>
  import {
    Form,
    FormGroup,
    But<PERSON>,
    ComposedModal,
    Modal<PERSON>ody,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Modal<PERSON>eader,
    InlineLoading,
  } from 'carbon-components-svelte';
  import { parse } from 'qs';
  import { push, querystring } from 'svelte-spa-router';
  import { Logo as LunaIcon } from 'src/assets/icons';
  import { EMPTY } from 'src/constants/app';
  import PublicLayout from 'src/components/layouts/PublicLayout.svelte';
  import { resetPassword } from 'src/service/auth.js';
  import { CustomTextInput, CustomPasswordInput } from 'src/components/common/input';
  import PrimaryButton from 'src/components/common/button/PrimaryButton.svelte';
  import 'src/styles/pages/reset.page.scss';

  const STATUS = {
    SUCCESS: 'Success',
    ERROR: 'Error',
  };

  const ERROR_MSG = {
    tmpPassword: { empty: '비밀번호를 입력해 주세요' },
    password: {
      empty: '새로운 비밀번호를 입력해 주세요',
      invalid: '8~64자의 영문 대소문자, 숫자, 특수문자를 모두 포함한 비밀번호를 입력해 주세요',
    },
    confirmPassword: {
      empty: '새 비밀번호를 다시 한 번 입력해 주세요',
      notMatch: '비밀번호가 일치하지 않습니다',
    },
  };

  let isLoading = false;
  let tmpPassword = EMPTY;
  let password = EMPTY;
  let confirmPassword = EMPTY;

  let errorResetPassword = {
    message: EMPTY,
    status: EMPTY,
  };

  let invalidOption = {
    tmpPassword: {
      invalid: false,
      invalidText: '',
    },
    password: {
      invalid: false,
      invalidText: '',
    },
    confirmPassword: {
      invalid: false,
      invalidText: '',
    },
  };

  async function onFormSubmit() {
    const formValues = { tmpPassword, password, confirmPassword };
    for (const el of Object.entries(formValues)) {
      const [key, value] = el;
      handleErrorValid(key, value);
    }
    const hasInvalid = Object.entries(invalidOption).some(([_, value]) => value.invalid);
    if (hasInvalid) return;

    isLoading = true;

    const urlParams = parse($querystring);
    const username = urlParams?.username || EMPTY;
    const payload = {
      username,
      tempPassword: tmpPassword,
      password,
    };
    const response = await resetPassword(payload);

    isLoading = false;
    const { code, message = EMPTY } = response || {};
    if (code === 200) {
      errorResetPassword = {
        message: '비밀번호가 변경되었습니다. 다시 로그인해 주세요.',
        status: STATUS.SUCCESS,
      };
    } else {
      errorResetPassword = {
        message: message || '발급받은 임시 비밀번호를 입력해 주세요.',
        status: STATUS.ERROR,
      };
    }
  }

  function validatePassword(password) {
    const lengthValid = password.length >= 8 && password.length <= 64;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecialChar = /[^A-Za-z0-9]/.test(password);

    return !(lengthValid && hasUppercase && hasLowercase && hasNumber && hasSpecialChar);
  }

  function checkMatchingPassword(value, updatedVal = EMPTY) {
    return (updatedVal || password) !== value;
  }

  function handleErrorValid(name, value, updatedVal = EMPTY) {
    const validators = {
      tmpPassword: [{ test: (v) => !v?.trim(), msg: ERROR_MSG.tmpPassword.empty }],
      password: [
        { test: (v) => !v?.trim(), msg: ERROR_MSG.password.empty },
        { test: (v) => validatePassword(v), msg: ERROR_MSG.password.invalid },
      ],
      confirmPassword: [
        { test: (v) => !v?.trim(), msg: ERROR_MSG.confirmPassword.empty },
        { test: (v, b) => checkMatchingPassword(v, b), msg: ERROR_MSG.confirmPassword.notMatch },
      ],
    };

    const rules = validators[name] || [];
    const rule = rules.find((r) => r.test(value, updatedVal));

    // This exception for checkMatchingPassword when both changing password and confirmPass
    const isException = name === 'password' && confirmPassword !== EMPTY;
    if (isException) {
      handleErrorValid('confirmPassword', confirmPassword, value);
    }

    invalidOption = {
      ...invalidOption,
      [name]: {
        invalid: Boolean(rule),
        invalidText: rule?.msg || EMPTY,
      },
    };
  }

  function onClickModalMessage() {
    if (errorResetPassword.status === STATUS.SUCCESS) {
      push('/sign-in');
    } else {
      errorResetPassword = {
        message: EMPTY,
        status: EMPTY,
      };
    }
  }

  function onInput(e, name) {
    const value = e?.detail?.target?.value || e?.detail;
    handleErrorValid(name, value);
  }
</script>

<PublicLayout>
  <Form
    class="form-reset-password"
    on:submit={(e) => {
      e.preventDefault();
      onFormSubmit();
    }}
  >
    <div class="form-logo"><LunaIcon /></div>
    <div class="text-input">
      <FormGroup>
        <CustomTextInput
          labelText="Temporary Password"
          placeholder="Enter the temporary password you received"
          autocomplete="one-time-code" 
          maxLength={64}
          bind:value={tmpPassword}
          invalid={invalidOption.tmpPassword.invalid}
          invalidText={invalidOption.tmpPassword.invalidText}
          on:input={(e) => onInput(e, 'tmpPassword')}
        />
      </FormGroup>
    </div>
    <div class="text-input">
      <FormGroup class="text-input-icon" data-invalid={invalidOption.password.invalid || password === EMPTY}>
        <CustomPasswordInput
          class="hidden-eye"
          name="password"
          labelText="New Password"
          maxLength={64}
          placeholder="Enter your new password"
          bind:value={password}
          invalid={invalidOption.password.invalid}
          invalidText={invalidOption.password.invalidText}
          on:input={(e) => onInput(e, 'password')}
        />
      </FormGroup>
    </div>
    <div class="text-input">
      <FormGroup
        class="text-input-icon"
        data-invalid={invalidOption.confirmPassword.invalid || confirmPassword === EMPTY}
      >
        <CustomPasswordInput
          class="hidden-eye"
          name="confirmPassword"
          labelText="Confirm Password"
          placeholder="Enter your new password again"
          bind:value={confirmPassword}
          invalid={invalidOption.confirmPassword.invalid}
          invalidText={invalidOption.confirmPassword.invalidText}
          on:input={(e) => onInput(e, 'confirmPassword')}
        />
      </FormGroup>
    </div>
    <Button class="submit-input" type="submit">
      {#if isLoading}
        <div class="g-custom-loading-button loading-box">
          <InlineLoading description="" />
        </div>
      {/if}
      Reset Password
    </Button>
  </Form>

  <ComposedModal open={errorResetPassword.status !== EMPTY} size="xs" preventCloseOnClickOutside>
    <ModalHeader>
      <p>{errorResetPassword.status}</p>
    </ModalHeader>
    <ModalBody>
      <p>{errorResetPassword.message}</p>
    </ModalBody>

    <ModalFooter>
      <PrimaryButton on:click={onClickModalMessage}>OK</PrimaryButton>
    </ModalFooter>
  </ComposedModal>
</PublicLayout>
