export const PRIME_BOOKING_SERVICE_RECALCULATE_WHITE_LIST = ['QR', 'AY', 'SQ', 'TR', 'AF', 'KL'];
export const PRIME_BOOKING_SERVICE_NOT_RECALCULATE_WHITE_LIST = ['HA', 'HAA'];

export const PRIME_BOOKING_SERVICE_WHITE_LIST = PRIME_BOOKING_SERVICE_RECALCULATE_WHITE_LIST.concat(
  PRIME_BOOKING_SERVICE_NOT_RECALCULATE_WHITE_LIST
);

export const PRIME_BOOKING_SERVICE_TYPE = { BAGGAGE: 'baggage', SEAT: 'seat', MEAL: 'meal', ETC: 'etc' };

export const PRIME_BOOKING_SERVICE_HAS_MULTIPLE = ['ONE KILOGRAM BAGGAGE', 'WEIGHT SYSTEM CHARGE'];

export const PRIME_BOOKING_SERVICE = {
  GROUP_AY: {
    BAGGAGE: [
      'PRE PAID BAGGAGE',
      '<PERSON>ECKE<PERSON> BAGGAGE UP TO 32KG',
      'EXCESS PIECE SPECIAL CHARGE',
      '1ST PREPAID BAG',
      '2ND PREPAID BAG',
      '3RD AND SUBSEQUENT PREPAID BAG',
      'SKI OR SNOWBOARD UP TO 23KG',
      'WEAPON',
      'SURFBOARD',
      'GOLF EQUIPMENT UP TO 50LB 23KG',
      'BICYCLE UP TO 23KG',
    ],
  },
  GROUP_TR: {
    BAGGAGE: [
      'Check-in Baggage 40Kg',
      'Check-in Baggage 35Kg',
      'Check-in Baggage 30Kg',
      'Check-in Baggage 25Kg',
      'Check-in Baggage 20Kg',
    ],
    MEAL: ['Dahl Curry Basmati Rice', 'Pumpkin Stew with Multigrain', 'Snack Pack'],
  },
  GROUP_HA_HAA: {
    ETC: ['Lei Greeting', 'Roundtrip Shuttle Service to Waikiki', 'Hassle-free roundtrip baggage handling'],
  },
  GROUP_SQ_QR: {
    BAGGAGE: ['ONE KILOGRAM BAGGAGE', 'EXCESS SIZE', 'EXCESS PIECE', 'EXCESS WEIGHT'],
  },
  GROUP_AF_KL: {
    BAGGAGE: 'LUGGAGE', // includes
  },
};

export const SERVICE_NAME = {
  CASH_UPGRADE: 'CASH UPGRADE',
  WEIGHT_SYSTEM_CHARGE: 'WEIGHT SYSTEM CHARGE', // QR only
};
