import { user } from 'src/store';

import { get } from 'svelte/store';

import { generateUUID, getTransactionId } from '../utils/requestHelper.js';

import request from './request.js';

const agencyID = get(user)?.agencyId;

export const getOrderRetrieve = async (params) => {
  const transactionID = generateUUID();
  sessionStorage.setItem(`transactionID-${params.order_id}`, transactionID);
  try {
    const response = await request({
      url: '/v1/ndc/order/retrieve',
      method: 'POST',
      data: {
        Sender: {
          TravelAgency: {
            AgencyID: agencyID,
            SiteCode: '01',
            AgencyName: 'string',
            ContactInfoRefID: [],
          },
        },
        PointOfSale: 'KR',
        TransactionID: transactionID,
        Query: {
          OrderID: params.order_id,
        },
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getAgencyProviderAirlineCode = async (params) => {
  try {
    const response = await request({
      url: '/v1/management/agency/provider',
      method: 'GET',
      params,
    });
    const res = response.data;
    const airlines = [
      ...new Map(
        res?.data?.Response?.AgencyProvider.flatMap((cur) => cur.Airline).map((airline) => [
          airline.AirlineCode,
          airline,
        ])
      ).values(),
    ];
    return airlines;
  } catch (error) {
    console.error(error);
  }
};

export const postOrderReshop = async (query) => {
  try {
    const response = await request({
      url: '/v1/ndc/order/reshop',
      method: 'POST',
      data: {
        Sender: {
          TravelAgency: {
            AgencyID: agencyID,
            SiteCode: '01',
          },
        },
        PointOfSale: 'KR',
        TransactionID: getTransactionId(query.OrderID),
        Query: query,
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getOrderCancel = async (params) => {
  try {
    const response = await request({
      url: '/v1/ndc/order/cancel',
      method: 'POST',
      data: {
        Sender: {
          TravelAgency: {
            AgencyID: agencyID,
            SiteCode: '01',
          },
        },
        PointOfSale: 'KR',
        TransactionID: getTransactionId(params.OrderID),
        Query: {
          OrderID: params.OrderID,
        },
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const postOrderChange = async (params) => {
  try {
    const response = await request({
      url: '/v1/ndc/order/change',
      method: 'POST',
      data: {
        Sender: {
          TravelAgency: {
            AgencyID: agencyID,
            SiteCode: '01',
            ContactInfoRefID: [],
          },
        },
        PointOfSale: 'KR',
        TransactionID: getTransactionId(params.query.OrderID),
        Query: params.query,
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const postOrderQuote = async (params) => {
  try {
    const response = await request({
      url: '/v1/ndc/orderQuote',
      method: 'POST',
      data: {
        Sender: {
          TravelAgency: {
            AgencyID: agencyID,
            SiteCode: '01',
            ContactInfoRefID: [],
          },
        },
        PointOfSale: 'KR',
        TransactionID: getTransactionId(params.query.OrderID),
        Query: params.query,
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const postSeatAvailability = async (params) => {
  try {
    const response = await request({
      url: '/v1/ndc/seatAvailability',
      method: 'POST',
      data: {
        Sender: {
          TravelAgency: {
            AgencyID: agencyID,
            SiteCode: '01',
          },
        },
        PointOfSale: 'KR',
        TransactionID: getTransactionId(params.query.Order.OrderID),
        Query: params.query,
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getPnrChangeHistory = async (params) => {
  try {
    const response = await request({
      url: '/v1/management/order/pnr-change-history',
      method: 'GET',
      params: {
        AgencyId: agencyID,
        ...params,
      },
    });
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};
