import { user } from 'src/store';
import { generateUUID, getTransactionId } from 'src/utils/requestHelper.js';

import { get } from 'svelte/store';

import { has } from 'lodash';
import { getValueFromUrl } from 'src/utils/appHelper.js';
import request from './request.js';

const agencyID = get(user)?.agencyId;

export const getAirShopping = async (params) => {
  const transactionID = generateUUID();
  const hash = window.location.hash;
  const urlParams = new URLSearchParams(hash.substring(2));
  const orderId = urlParams.get('orderId');
  sessionStorage.setItem(`transactionID-${orderId}`, transactionID);
  try {
    const response = await request({
      url: '/v1/ndc/air-shopping',
      method: 'POST',
      data: {
        Sender: {
          TravelAgency: {
            AgencyID: agencyID,
            SiteCode: '01',
          },
        },
        PointOfSale: 'KR',
        TransactionID: transactionID,
        Query: params.Query,
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getOfferPrice = async (params) => {
  const hash = window.location.hash;
  const urlParams = new URLSearchParams(hash.substring(2));
  const orderId = urlParams.get('orderId');
  try {
    const response = await request({
      url: '/v1/ndc/offer/price',
      method: 'POST',
      data: {
        Sender: {
          TravelAgency: {
            AgencyID: agencyID,
            SiteCode: '01',
            ContactInfoRefID: [],
          },
        },
        PointOfSale: 'KR',
        TransactionID: getTransactionId(orderId),
        Query: params.Query,
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getOffer = async (params) => {
  const hash = window.location.hash;
  const urlParams = new URLSearchParams(hash.substring(2));
  const orderId = urlParams.get('orderId');
  try {
    const response = await request({
      url: '/v1/ndc/order',
      method: 'POST',
      data: {
        Sender: {
          TravelAgency: {
            AgencyID: agencyID,
            SiteCode: '01',
          },
        },
        PointOfSale: 'KR',
        TransactionID: getTransactionId(orderId),
        Query: params.Query,
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getServiceList = async (params) => {
  const orderId = has(params, 'Order.OrderID') ? params.Order.OrderID : getValueFromUrl('orderId');

  try {
    const response = await request({
      url: '/v1/ndc/serviceList',
      method: 'POST',
      data: {
        Sender: {
          TravelAgency: {
            AgencyID: agencyID,
            SiteCode: '01',
          },
        },
        PointOfSale: 'KR',
        TransactionID: getTransactionId(orderId),
        Query: params,
      },
    });
    return response.data;
  } catch (error) {
    console.log(error);
  }
};
