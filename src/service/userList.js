import { get } from 'svelte/store';

import { user } from 'src/store';
import { EMPTY } from 'src/constants/app';

import request from './request';

const username = get(user)?.username;
const agencyId = get(user)?.agencyId;

const getUserList = async (params, data) => {
  try {
    const response = await request({
      url: '/v1/user/list',
      method: 'POST',
      params,
      data: {
        ...data,
        username: EMPTY, // TODO: need confirmed
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

const deleteUser = async (username) => {
  try {
    const response = await request({
      url: '/v1/user/delete',
      method: 'DELETE',
      data: {
        username,
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

const addNewUser = async (data) => {
  try {
    const response = await request({
      url: '/v1/user/sign-up',
      method: 'POST',
      data: {
        ...data,
        agencyId,
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export default {
  getUserList,
  deleteUser,
  addNewUser,
};
