import { derived } from 'svelte/store';

import { user } from 'src/store';

import {
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  isAdmin,
  isTicketingUser,
  isRegularUser,
  getRolePermissions,
} from './rolePermissions.js';

export const userRole = derived(user, ($user) => $user?.role || null);

export const isUserAdmin = derived(userRole, ($userRole) => isAdmin($userRole));

export const isUserTicketingUser = derived(userRole, ($userRole) => isTicketingUser($userRole));

export const isUserRegularUser = derived(userRole, ($userRole) => isRegularUser($userRole));

export function createPermissionStore(permission) {
  return derived(userRole, ($userRole) => hasPermission($userRole, permission));
}

export function createPermissionsStore(permissions, requireAll = false) {
  return derived(userRole, ($userRole) => {
    if (requireAll) {
      return hasAllPermissions($userRole, permissions);
    } else {
      return hasAnyPermission($userRole, permissions);
    }
  });
}

export const userPermissions = derived(userRole, ($userRole) => getRolePermissions($userRole));

export function checkPermission(permission) {
  let currentUser;
  const unsubscribe = user.subscribe((value) => (currentUser = value));
  unsubscribe();

  return hasPermission(currentUser?.role, permission);
}

export function checkPermissions(permissions, requireAll = false) {
  let currentUser;
  const unsubscribe = user.subscribe((value) => (currentUser = value));
  unsubscribe();

  const userRole = currentUser?.role;

  if (requireAll) {
    return hasAllPermissions(userRole, permissions);
  } else {
    return hasAnyPermission(userRole, permissions);
  }
}

export function getCurrentUserRole() {
  let currentUser;
  const unsubscribe = user.subscribe((value) => (currentUser = value));
  unsubscribe();

  return currentUser?.role || null;
}
