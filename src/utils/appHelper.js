import { find, reduce, has, filter, map, sortBy, uniqWith, isEqual, uniq, compact, cloneDeep, set } from 'lodash';

import { CABIN_CODE_MAP, EMPTY, TYPE_TICKET } from 'src/constants/app.js';

import { formatDateTime } from './DateTime.js';

export function formatPriceNumber(price) {
  try {
    return price.toLocaleString();
  } catch {
    return EMPTY;
  }
}

export function nameTypeTicket(type) {
  let findType = TYPE_TICKET.find((ticket) => ticket.key === type);
  return findType ? findType.value : type;
}

export const standardObj = (obj) => {
  return reduce(
    obj,
    (result, value, key) => {
      if (result[key] === undefined || result[key] === null) return { ...result, [key]: EMPTY };
      return result;
    },
    obj
  );
};

export const statusTickKet = (status, type) => {
  const statusMap = {
    I: {
      T: 'TICKETED',
      J: 'ISSUED',
      Y: 'ISSUED',
    },
    V: 'VOIDED',
    R: 'REFUNDED',
    E: 'EXCHANGED',
  };
  const statusTypeMap = statusMap[status];
  if (statusTypeMap) {
    if (typeof statusTypeMap === 'string') {
      return statusTypeMap;
    } else if (statusTypeMap[type]) {
      return statusTypeMap[type];
    }
  }
  return status;
};

export const createEmptyArray = (length) => {
  const array = [];
  for (let i = 0; i < length; i++) {
    array.push(EMPTY);
  }
  return array;
};

export function convertToFormattedKoreanPhoneNumber(phoneNumber) {
  const digitsOnly = phoneNumber.replace(/\D/g, EMPTY);
  const numberWithCountryCode = digitsOnly.startsWith('82') ? digitsOnly : `82${digitsOnly}`;
  const pattern = /^(82)(\d{2})(\d{3,4})(\d{4})$/;

  return numberWithCountryCode.replace(pattern, '+$1-$2-$3-$4');
}

export function formatDate(dateString, type) {
  if (!dateString?.trim()) return EMPTY;
  let dateDdMmY = EMPTY;
  let time = EMPTY;
  if (dateString) {
    [dateDdMmY, time] = dateString?.split(' ');
  }
  if (!dateString) return EMPTY;
  const date = new Date(dateDdMmY);
  const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

  const day = date.getDate();
  const month = monthNames[date.getMonth()];
  const year = date.getFullYear();
  if (type === 'date') return `${('0' + day).slice(-2)}/${month}/${year}`;
  else if (type === 'datetime') {
    return `${('0' + day).slice(-2)}/${month}/${year} ${time || EMPTY}`;
  }
}

export function getCabinName(cabinCode) {
  return CABIN_CODE_MAP[cabinCode] || cabinCode || EMPTY;
}

export function getMaxWeightAllowance(baggageAllowance, PaxJourneyID) {
  let maximumWeightAllowance;
  let pieceAllowance;

  const baggageAllowanceHasCheckedOrCheckedBag = baggageAllowance.filter(
    ({ TypeCode, PaxJourneyRefID }) =>
      ['Checked', 'CheckedBag'].includes(TypeCode) && PaxJourneyRefID.includes(PaxJourneyID)
  );
  for (const { MaximumWeightAllowance, PieceAllowance } of baggageAllowanceHasCheckedOrCheckedBag) {
    if (
      (!maximumWeightAllowance || maximumWeightAllowance?.Value < MaximumWeightAllowance?.Value) &&
      has(MaximumWeightAllowance, 'Value')
    ) {
      maximumWeightAllowance = MaximumWeightAllowance;
    }
    if (!pieceAllowance) pieceAllowance = PieceAllowance;
  }
  if (pieceAllowance && maximumWeightAllowance)
    return `${pieceAllowance}pc(${maximumWeightAllowance.Value ?? EMPTY}${maximumWeightAllowance.UnitCode ?? EMPTY})`;
  if (maximumWeightAllowance)
    return `${maximumWeightAllowance.Value ?? EMPTY}${maximumWeightAllowance.UnitCode ?? EMPTY}`;
  if (pieceAllowance) return `${pieceAllowance}pc`;
  return null;
}

export function isPropertyEmpty(obj) {
  for (let key in obj) {
    if (!obj[key] || obj[key].trim() === EMPTY) {
      return true;
    }
  }
  return false;
}

export function formatQueryParams(option, arrKeys) {
  const params = reduce(
    option,
    (result, value, key) => {
      if (value === EMPTY || value?.length === 0 || value === '전체' || value.includes('전체')) {
        return result;
      }
      if (arrKeys.includes(key)) {
        value = formatDateTime(value);
      }
      return { ...result, [key]: value };
    },
    {}
  );
  return params;
}

export function formatPenaltyDetail(Amounts, Application, codeDetail) {
  let data = EMPTY;
  const findMin = Amounts.find((elm) => elm.AmountApplication === 'MIN');
  const findMax = Amounts.find((elm) => elm.AmountApplication === 'MAX');
  if (findMin) {
    const currencyAmountCodeMin = formatPriceNumber(findMin.CurrencyAmountValue) + findMin.Code;
    data = `${codeDetail[Application.Code]} 최소 ${currencyAmountCodeMin}`;
  }
  if (findMax) {
    const currencyAmountCodeMax = formatPriceNumber(findMax.CurrencyAmountValue) + findMax.Code;
    data = `${codeDetail[Application.Code]} 최대 ${currencyAmountCodeMax}`;
  }
  if (findMin && findMax) {
    const currencyAmountCodeMin = formatPriceNumber(findMin.CurrencyAmountValue) + findMin.Code;
    const currencyAmountCodeMax = formatPriceNumber(findMax.CurrencyAmountValue) + findMax.Code;
    if (currencyAmountCodeMin === currencyAmountCodeMax) {
      data = `${codeDetail[Application.Code]} ${currencyAmountCodeMin}`;
    } else {
      data = `${codeDetail[Application.Code]} 최소 ${currencyAmountCodeMin} 최대 ${currencyAmountCodeMax}`;
    }
  }
  if (!findMin && !findMax) {
    data = `${codeDetail[Application.Code]} ${formatPriceNumber(Amounts[0]?.CurrencyAmountValue ?? EMPTY)} ${
      Amounts[0]?.Code ?? EMPTY
    }`;
  }
  return data;
}

export function getDataColsRuleCoveredServices(Penalty, Remarks, airlineID, findJourneyOverview) {
  const codeDetail = {
    1: airlineID !== 'LJ' ? '노쇼' : '수속마감/NO-SHOW',
    2: airlineID !== 'LJ' ? '출발전' : '출발전',
    3: airlineID !== 'LJ' ? '출발후' : '부분환불',
  };
  const sortCodeDetail = {
    1: 3,
    2: 1,
    3: 2,
  };
  let INIT_COL_INDEX = {
    [`${has(Penalty, 'Change') ? 'change' : EMPTY}`]: {
      label: `변경(${Penalty?.Change === 'true' ? '가능' : '불가'})`,
      data: [],
    },
    [`${has(Penalty, 'Refund') ? 'cancel' : EMPTY}`]: {
      label: `취소(${Penalty?.Refund === 'true' ? '가능' : '불가'})`,
      data: [],
    },
    [`${has(Penalty, 'NoShow') ? 'noShow' : EMPTY}`]: {
      label: `NoShow(${Penalty.NoShow === 'true' ? '가능' : '불가'})`,
      data: [],
    },
  };
  let COL_INDEX_ROW_1;
  const COL_INDEX_ROW_2 = filter(Remarks, (itemRemark) => itemRemark.includes('Earliest'));
  const COL_INDEX_ROW_3 = filter(Remarks, (itemRemark) => itemRemark.includes('Latest'));
  const COL_INDEX_ROW_4 = map(findJourneyOverview?.PriceClassInfo?.Descriptions, (it) => {
    const { DescID, Text } = it;
    if (DescID && !Text) return EMPTY;
    if (!DescID && Text) return Text;
    return `${DescID}/${Text}`;
  });
  if (airlineID === 'LJ') {
    INIT_COL_INDEX.lj = {
      label: '예약부도 위약금 (No-Show)',
      data: [],
    };
    COL_INDEX_ROW_1 = INIT_COL_INDEX;
    let detailChange = [];
    let detailCancel = [];

    for (const detail of Penalty?.Detail ?? []) {
      if (detail.Type === 'Change') detailChange.push(detail);
      if (detail.Type === 'Cancel') detailCancel.push(detail);
    }
    // get data COL_INDEX_ROW_1.cancel
    detailCancel = sortBy(detailCancel, (it) => it.Amounts[0]?.AmountApplication * 1).reverse();
    const detailCancelCodeOneAndThree = filter(detailCancel, (detailCancelItem) =>
      ['1', '3'].includes(detailCancelItem?.Application.Code)
    );
    const detailCancelCodeTwo = filter(detailCancel, (detailCancelItem) => detailCancelItem?.Application.Code == 2);
    //Application/Code === 2
    for (let i = 0; i < detailCancelCodeTwo.length; i++) {
      const { Application, Amounts } = detailCancelCodeTwo[i];
      let amountItem = 0;
      let currentData;
      for (const amount of Amounts) {
        amountItem += amount?.CurrencyAmountValue ?? 0;
      }
      if (i === detailCancelCodeTwo.length - 1) {
        currentData = {
          index: sortCodeDetail[Application.Code],
          data: `${codeDetail[Application.Code]} (${Amounts[0]?.AmountApplication}일 전~수속마감) ${amountItem} ${
            Amounts[0]?.Code ?? 'KRW'
          }`,
        };
      } else {
        currentData = {
          index: sortCodeDetail[Application.Code],
          data: `${codeDetail[Application.Code]} (${Amounts[0]?.AmountApplication}일 전~${
            detailCancel[i + 1]?.Amounts[0]?.AmountApplication * 1 + 1
          }일 전) ${amountItem} ${Amounts[0]?.Code ?? 'KRW'}`,
        };
      }
      COL_INDEX_ROW_1.cancel.data.push(currentData);
    }
    //Application/Code === 1,3
    for (const itemDetailCancelCodeOneAndThree of detailCancelCodeOneAndThree) {
      const { Application, Amounts } = itemDetailCancelCodeOneAndThree;
      let amountItem = 0;
      for (const amount of Amounts) {
        amountItem += amount?.CurrencyAmountValue ?? 0;
      }

      COL_INDEX_ROW_1.cancel.data.push({
        index: sortCodeDetail[Application.Code],
        data: `${codeDetail[Application.Code]} ${amountItem} ${Amounts[0]?.Code ?? 'KRW'}`,
      });
    }
    // get data COL_INDEX_ROW_1.change
    for (const detailChangeItem of detailChange) {
      const { Application, Amounts } = detailChangeItem;
      let amountItem = 0;
      for (const amount of Amounts) {
        amountItem += amount?.CurrencyAmountValue ?? 0;
      }
      if (Application.Code === '1') {
        COL_INDEX_ROW_1.lj.data.push(`NO-SHOW 수수료 ${amountItem} ${Amounts[0]?.Code ?? 'KRW'}`);
        break;
      }
      COL_INDEX_ROW_1.change.data.push({
        index: sortCodeDetail[Application.Code],
        data: `${codeDetail[Application.Code]} ${amountItem} ${Amounts[0]?.Code ?? 'KRW'}`,
      });
    }
  } else {
    COL_INDEX_ROW_1 = reduce(
      Penalty?.Detail,
      (result, itDetail) => {
        const { Type, Amounts, Application } = itDetail;
        const data = formatPenaltyDetail(Amounts, Application, codeDetail);
        if (Type === 'Change' && has(result, 'change'))
          result.change.data = sortBy(
            [
              ...result.change.data,
              {
                index: sortCodeDetail[Application.Code],
                data,
              },
            ],
            'index'
          );
        if (['Cancel', 'Refund'].includes(Type) && has(result, 'cancel'))
          result.cancel.data = sortBy(
            [
              ...result.cancel.data,
              {
                index: sortCodeDetail[Application.Code],
                data,
              },
            ],
            'index'
          );
        return result;
      },
      INIT_COL_INDEX
    );
  }

  if (has(COL_INDEX_ROW_1, 'cancel')) {
    COL_INDEX_ROW_1.cancel.data = COL_INDEX_ROW_1.cancel.data.map((item) => item.data);
  }
  COL_INDEX_ROW_1.change.data = COL_INDEX_ROW_1.change.data.map((item) => item.data);
  return [COL_INDEX_ROW_1, COL_INDEX_ROW_2, COL_INDEX_ROW_3, COL_INDEX_ROW_4];
}

export function exportExcelFileName(dataBlob, name) {
  const today = new Date().toJSON();
  const [date] = today.split('T');

  const href = URL.createObjectURL(dataBlob);
  const link = document.createElement('a');
  link.href = href;
  link.setAttribute('download', `${name}-${date}.xlsx`);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(href);
}

export function getMainBookingReference(bookingReference) {
  if (bookingReference?.length === 1) return bookingReference[0];
  let findB = find(
    bookingReference || [],
    (itB) => itB?.AirlineID === 'LJ' || (!has(itB, 'OtherID') && !has(itB, 'Type'))
  );
  return findB;
}

export const getWidthOfText = (text, fontSize, fontFamily) => {
  const span = document.createElement('span');
  span.style.fontSize = fontSize;
  span.style.fontFamily = fontFamily;
  span.textContent = text;
  document.body.appendChild(span);
  const width = span.offsetWidth;
  document.body.removeChild(span);
  return width;
};

export const getDefaultFontFamily = (element) => {
  const defaultFontFamily = window.getComputedStyle(element, null).getPropertyValue('font-family');
  return defaultFontFamily;
};

export const getDefaultFontSize = (element) => {
  const defaultFontSize = window.getComputedStyle(element, null).getPropertyValue('font-size');
  return defaultFontSize;
};

export const getOtherPnrByBookingReference = (bookingReference) => {
  const checkLj = bookingReference?.find((bk) => bk?.AirlineID === 'LJ');
  if (checkLj) return EMPTY;
  const hasOtherId = bookingReference?.find((bk) => has(bk, 'OtherID') && bk?.OtherID);
  if (hasOtherId) return hasOtherId?.Id ?? EMPTY;
  return EMPTY;
};

export const getUrlLogoAirline = (airlineId) => {
  let _airlineId;
  switch (airlineId) {
    case 'HAA':
      _airlineId = 'HA';
      break;
    case 'LXA':
      _airlineId = 'LX';
      break;
    default:
      _airlineId = airlineId;
  }
  return `https://cdn.ndcaggregator.kr/logo/${_airlineId}.png`;
};

export const convertFlightTime = (flightTime) => {
  if (!flightTime) return EMPTY;
  const durationRegex = /P(?:(\d+)Y)?(?:(\d+)M)?(?:(\d+)D)?T?(?:(\d+)H)?(?:(\d+)M)?(?:(\d+(?:\.\d+)?)S)?/;
  const timeRegex = /PT(?:(\d+)H)?(?:(\d+)M)?/;
  const match = flightTime?.match(durationRegex);

  let days = 0;
  let hours = 0;
  let minutes = 0;

  if (match[3]) days = parseInt(match[3]);

  if (match[4]) {
    hours = parseInt(match[4]);
    if (hours >= 24) {
      days += Math.floor(hours / 24);
      hours = hours % 24;
    }
  }

  if (match[5]) minutes = parseInt(match[5]);

  if (!match[1] && !match[2] && !match[3] && !match[4] && !match[5] && !match[6]) {
    // case PT18H45M
    const timeMatch = flightTime.match(timeRegex);
    if (timeMatch[1]) {
      hours = parseInt(timeMatch[1]);
      if (hours >= 24) {
        days += Math.floor(hours / 24);
        hours = hours % 24;
      }
    }
    if (timeMatch[2]) {
      minutes = parseInt(timeMatch[2]);
    }
  }
  return `${days === 0 ? EMPTY : days + '일'}${hours}시간${minutes}분`;
};

export const isValidEmail = (text) => {
  const regexEmail = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return !regexEmail.test(text);
};

export function generatePopupCoveredServices(
  PaxSegmentList,
  JourneyOverview,
  PaxJourneyList,
  FareComponent,
  airlineID
) {
  // let fareComponent = FareDetail[0]?.FareComponent ?? [];
  let PaxSegmentRefIDList = uniqWith(
    FareComponent.map((item) => item.PaxSegmentRefID),
    isEqual
  );
  let headers = [
    {
      key: 'LABEL',
      value: {
        label: ['여정'],
        subLabel: ['FareBasis', 'PriceClass'],
      },
    },
  ];
  let rows = [];
  let ROW_1 = {
      id: 'ROW_1',
      LABEL: '페널티',
    },
    ROW_2 = {
      id: 'ROW_2',
      LABEL: '최소 체류일',
    },
    ROW_3 = {
      id: 'ROW_3',
      LABEL: '최대 체류일',
    },
    ROW_4 = {
      id: 'ROW_4',
      LABEL: '포함 서비스',
    };

  for (let index = 0; index < PaxSegmentRefIDList.length; index++) {
    const PaxSegmentRefID = PaxSegmentRefIDList[index];
    const FareBasis = FareComponent.find(
      (item) => isEqual(PaxSegmentRefID, item.PaxSegmentRefID) && has(item, 'FareBasis')
    )?.FareBasis;
    const Penalty = FareComponent.find(
      (item) => isEqual(PaxSegmentRefID, item.PaxSegmentRefID) && has(item, 'Penalty')
    )?.Penalty;
    const Remarks = FareComponent.reduce((RemarksItem, item) => {
      if (isEqual(PaxSegmentRefID, item.PaxSegmentRefID) && has(item, 'Remarks'))
        return [...RemarksItem, ...item.Remarks];
      return RemarksItem;
    }, []);

    if (!PaxSegmentRefID.length || (!has(Penalty, 'Change') && !has(Penalty, 'Refund'))) break;
    const label = map(PaxSegmentRefID, (paxSegRefIdItem) => {
      const PaxSegment = find(PaxSegmentList, (paxSegItem) => paxSegItem.PaxSegmentID === paxSegRefIdItem);
      return `${PaxSegment?.Departure?.AirportCode}-${PaxSegment?.Arrival?.AirportCode}`;
    });
    let subLabel = [];
    const findPaxJourneyID = find(PaxJourneyList, (paxJourneyItem) =>
      paxJourneyItem.PaxSegmentRefID?.includes(PaxSegmentRefID[0])
    );
    const findJourneyOverview = find(
      JourneyOverview,
      (JourneyItem) => JourneyItem.PaxJourneyRefID === findPaxJourneyID?.PaxJourneyID
    );
    subLabel = [...subLabel, FareBasis?.FareBasisCode || EMPTY, findJourneyOverview?.PriceClassInfo?.Name || EMPTY];
    headers = [
      ...headers,
      {
        key: `COL_${index}`,
        value: {
          label,
          subLabel,
        },
      },
    ];
    const [COL_INDEX_ROW_1, COL_INDEX_ROW_2, COL_INDEX_ROW_3, COL_INDEX_ROW_4] = getDataColsRuleCoveredServices(
      Penalty,
      Remarks,
      airlineID,
      findJourneyOverview
    );

    ROW_1 = {
      ...ROW_1,
      [`COL_${index}`]: COL_INDEX_ROW_1,
    };
    ROW_2 = {
      ...ROW_2,
      [`COL_${index}`]: COL_INDEX_ROW_2,
    };
    ROW_3 = {
      ...ROW_3,
      [`COL_${index}`]: COL_INDEX_ROW_3,
    };
    ROW_4 = {
      ...ROW_4,
      [`COL_${index}`]: COL_INDEX_ROW_4,
    };
  }

  rows = [ROW_1, ROW_2, ROW_3, ROW_4];
  return {
    headers,
    rows,
  };
}

export function sortPaxListByPtc(PaxList) {
  const sortByPtc = { ADT: 1, CHD: 2, CNN: 2, INF: 3 };
  return PaxList.sort((a, b) => sortByPtc[a.Ptc] - sortByPtc[b.Ptc]);
}

export function splitConvertArrayId(modelsId) {
  let arrId = [];
  if (typeof modelsId === 'string') {
    // Common case: (1, 2) / (1 2) / (1, 2 3)
    arrId = compact(
      modelsId
        .replace(/,/g, ' ')
        .split(' ')
        .map((item) => item.trim())
    );
  } else {
    for (const item of modelsId) {
      arrId = [...arrId, ...item.split(' ')];
    }
  }
  return uniq(arrId);
}

export const formatInfoSegment = (segment) => {
  if (!segment) return EMPTY;
  return `${segment?.MarketingCarrier?.AirlineID}${segment?.MarketingCarrier?.FlightNumber.padStart(4, '0')} ${
    segment?.Departure?.AirportCode
  }-${segment?.Arrival?.AirportCode} ${formatDate(segment?.Departure?.Date, 'date')}`;
};

export const getPassengerNamePtc = (paxListItem) => {
  if (!paxListItem) return EMPTY;
  if (paxListItem?.Individual)
    return `${paxListItem?.Individual?.Surname ?? EMPTY}/${paxListItem?.Individual?.GivenName ?? EMPTY}(${
      paxListItem?.Ptc ?? EMPTY
    })`;
  return paxListItem?.Ptc ?? EMPTY;
};

export const sleep = (s) => new Promise((r) => setTimeout(r, s));

export const groupData = (dataTable) => {
  let newDataTable = [];
  for (let item of dataTable) {
    const checkHasItem = newDataTable.find(
      (it) =>
        it.PaxID === item.PaxID &&
        it.status === item.status &&
        it.name === item.name &&
        it.OrderItemID === item.OrderItemID
    );
    if (!checkHasItem) {
      newDataTable.push(item);
      continue;
    }
    checkHasItem.segments = uniqWith(checkHasItem.segments.concat(item.segments), isEqual);
  }
  return newDataTable;
};

export const getSsrData = (resOrderRetrieve, airlineID) => {
  let newSsrTable = [];
  const { Order, DataLists } = resOrderRetrieve?.data || {};
  const { PaxSegmentList, PaxList } = DataLists || {};
  const serviceList = uniqWith(
    Order?.OrderItem?.flatMap(
      (item) =>
        item?.Service.map((elm) => ({ ...elm, OrderItemID: item.OrderItemID, FareDetail: item.FareDetail })) ?? []
    ),
    isEqual
  );
  //AF,KL
  const ServiceRefIDsByTicketDocList = DataLists?.TicketDocList?.flatMap((ticketDoc) => {
    return ticketDoc.TicketDocument.flatMap(({ CouponInfos }) =>
      CouponInfos.flatMap((coupItem) => coupItem.ServiceRefID)
    );
  });

  newSsrTable = map(serviceList ?? [], (item, index) => {
    const findPaxSegment = PaxSegmentList.find((paxSegment) => paxSegment.PaxSegmentID === item.PaxSegmentRefID);
    const findPax = PaxList.find((pax) => pax.PaxID === item.PaxRefID);
    let checked = false;
    if (['AF', 'KL'].includes(airlineID)) {
      if (!ServiceRefIDsByTicketDocList.includes(item.ServiceID)) checked = true;
    }
    let name = EMPTY;
    if (!has(item, 'Definition') && !has(item, 'SelectedSeat')) {
      name = 'Segment';
    }
    if (has(item, 'Definition')) {
      name = `${item?.Definition?.Name ?? EMPTY}`;
    }

    if (has(item, 'SelectedSeat')) {
      name += ` ${item?.SelectedSeat?.Row ?? EMPTY}${item?.SelectedSeat?.Column ?? EMPTY}`;
    }
    return {
      id: index,
      airlineID: findPaxSegment?.MarketingCarrier?.AirlineID ?? item?.Owner ?? EMPTY,
      givenNameAndPtc: getPassengerNamePtc(findPax),
      name,
      status: item?.Status ?? EMPTY,
      segments: [formatInfoSegment(findPaxSegment)],
      Ptc: findPax?.Ptc ?? EMPTY,
      PaxID: item['PaxRefID'],
      OrderItemID: item.OrderItemID,
      FareDetail: item.FareDetail,
      checked,
    };
  });
  return newSsrTable;
};

export const getAmountAndOrderItemIDList = (ssrTable, PaymentList) => {
  let Amount = 0;
  let CurCode = EMPTY;
  let OrderItemIDList = [];
  const orderItemIdsSuccessFull = PaymentList.filter((item) => ['SUCCESSFUL', '707'].includes(item.Status)).flatMap(
    (item) => item.OrderItemID
  );
  ssrTable.forEach(({ OrderItemID, checked, FareDetail }) => {
    if (checked && !orderItemIdsSuccessFull.includes(OrderItemID)) {
      OrderItemIDList.push(OrderItemID);
      FareDetail.forEach((fare) => {
        Amount += fare?.BaseAmount?.Amount ?? 0;
        if (!CurCode) CurCode = fare?.BaseAmount?.CurCode;
      });
    }
  });
  return { Amount, CurCode, OrderItemIDList };
};

export const getValueFromUrl = (key) => {
  const hash = window.location.hash;
  const urlParams = new URLSearchParams(hash.substring(2));
  const value = urlParams.get(key);

  return value ?? EMPTY;
};

export function mappingByKeysValue(mappingKeys, obj) {
  const clonedObj = cloneDeep(obj);

  mappingKeys.forEach((item) => {
    const { key, value } = item;
    if (has(clonedObj, key)) {
      set(clonedObj, key, value);
    }
  });

  return clonedObj;
}

export function calcCancelTicketFee(responseOrderChange) {
  const reshopOffers = responseOrderChange?.data.ReshopOffers || [];
  let totalAmount = 0;
  let totalAmounCurCode = EMPTY;
  let penalty = 0;
  let penaltyCurCode = EMPTY;
  let refundAmount = 0;
  let refundAmountCurCode = EMPTY;
  for (const reshopOffer of reshopOffers) {
    const isCheckPenalty = reshopOffer.DeleteOrderItem?.find((item) => has(item, 'PenaltyAmount.Total.Amount'));
    if (!isCheckPenalty) {
      totalAmount += reshopOffer?.TotalPrice?.TotalAmount?.Amount || 0;
      totalAmounCurCode = reshopOffer?.TotalPrice?.TotalAmount?.CurCode || 'KRW';
    } else {
      for (const item of reshopOffer?.DeleteOrderItem) {
        if (has(item, 'PenaltyAmount.Total.Amount')) {
          totalAmount += item?.OriginalOrderItem?.Total?.Amount || 0;
        }
      }
      totalAmounCurCode = reshopOffer?.DeleteOrderItem[0]?.OriginalOrderItem?.Total?.CurCode || 'KRW';
    }

    for (const item of reshopOffer?.DeleteOrderItem) {
      penalty += item?.PenaltyAmount?.Total?.Amount || 0;
      penaltyCurCode = item?.PenaltyAmount?.Total?.CurCode || 'KRW';
    }
    // tinhtoan refundAmount
    refundAmount += reshopOffer?.TotalPrice?.TotalAmount?.Amount || 0;
    refundAmountCurCode = reshopOffer?.TotalPrice?.TotalAmount?.CurCode || 'KRW';
  }

  totalAmount = `${formatPriceNumber(totalAmount) || 0} ${totalAmounCurCode}`;

  penalty = `${formatPriceNumber(penalty) || 0} ${penaltyCurCode}`;

  refundAmount = `${formatPriceNumber(Math.abs(refundAmount)) || 0} ${refundAmountCurCode}`;

  return {
    totalAmount,
    penalty,
    refundAmount,
  };
}
