$modalWidth: calc(100vw - 64px);

.management-custom-popup {
  .bx--modal-footer {
    height: auto;
    .bx--btn {
      background-color: #000000;
      height: 40px !important;
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }
  .bx--modal-container {
    width: $modalWidth;
    box-sizing: content-box;
  }
  .bx--modal-content {
    padding: 0 16px 0 16px;
    margin-bottom: 16px;
    height: auto !important;
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #c4c4c4;
      border-radius: 20px;
    }
  }
}

.table-management__ticket-popup {
  .popup-header,
  .popup-footer {
    h2 {
      font-size: 20px;
      overflow: hidden;
      padding-left: 0.8em;
      padding-top: 0.75rem;
      margin-bottom: 0.4em;
    }
  }
  .popup-header ul {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    min-height: 76px;
    column-gap: 2px;
    background-color: white;
  }
  .popup-header ul li {
    @include center-content-flex-y;
    border-left: 2px solid #e0e0e0;
    &:nth-child(4n - 3) {
      border: none;
    }
    &:nth-child(1),
    &:nth-child(2),
    &:nth-child(3),
    &:nth-child(4) {
      padding: 16px 16px 4px 16px;
    }
    &:nth-child(5),
    &:nth-child(6),
    &:nth-child(7),
    &:nth-child(8) {
      padding: 4px 16px 16px 16px;
    }
    .item-label {
      font-weight: 700;
      min-width: 100px;
    }
  }
  .popup-footer .popup-footer__container {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    column-gap: 2px;
    background-color: #e0e0e0;

    .popup-footer__container--col {
      background-color: white;
      padding: 16px;
    }

    .popup-footer__container--col-item {
      margin-top: 8px;
      display: flex;
      text-transform: capitalize;
      .item-label {
        font-weight: 700;
        min-width: 120px;
      }
      .item-content {
        min-width: 150px;
      }
      .text-align {
        text-align: right;
      }

      ul li {
        @include center-content-flex-y;
        height: 26px;
        .tax-code {
          min-width: 40px;
        }
        .amount-money {
          flex-grow: 1;
        }
      }
      ul {
        max-height: 264px;
        overflow: hide overlay;
        overflow-y: scroll;
        padding-right: 8px;
        margin-left: 12px;
        &::-webkit-scrollbar {
          width: 4px;
        }
        &::-webkit-scrollbar-track {
          background: #ffffff;
        }
        &::-webkit-scrollbar-thumb {
          background-color: #c4c4c4;
          border-radius: 20px;
        }
      }
    }
  }
  .popup-table {
    padding: 32px 0;
    tbody td {
      background-color: white !important;
    }
    .popup-ticket-table .bx--table-expand__button {
      display: none;
    }
  }
}

.management-popup__covered-services {
  .bx--modal-container {
    background-color: rgba(244, 244, 244, 1);
    max-width: $modalWidth;
    width: auto;
  }
  .bx--data-table {
    min-width: 860px;
    // padding: 0 100px;

    width: auto;
    ul {
      margin-left: 24px;
    }
    ul li {
      padding-left: 8px;
      list-style: '•';
      font-weight: 400;
      font-size: 14px;
      &:first-child {
        margin-top: 8px;
      }
    }
    padding: 16px !important;
    h2 {
      font-size: 14px;
      font-weight: 700;
    }
    thead {
      box-sizing: border-box;
      min-height: 94px;
      height: auto;
      background-color: #ffffff;
    }

    th,
    td {
      min-width: 200px !important;
      background-color: transparent;
      &:not(:first-child) {
        padding-left: 0;
        padding-right: 0;
      }
    }
  }
  .bx--data-table thead tr {
    background-color: transparent;
  }
  .covered-services__container {
    @include center-content-flex;
    width: 100%;
  }

  .mb-8px {
    margin-bottom: 8px;
  }
  hr {
    border-top: 1px solid rgb(224, 224, 224);
    border-radius: none;
  }

  .p-8px {
    padding: 16px 0;
  }

  .hidden {
    opacity: 0;
  }
}

.management-popup__covered-services.height-modal {
  .bx--modal-content {
    height: 35vh !important;
    width: min-content;
  }
  .bx--modal-container {
    transform: scale(0.92) !important;
  }
}
