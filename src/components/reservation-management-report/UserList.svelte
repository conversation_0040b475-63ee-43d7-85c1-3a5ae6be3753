<script>
  import { onMount } from 'svelte';
  import {
    Form,
    FormGroup,
    Button,
    DataTable,
    DatePicker,
    DatePickerInput,
    Pagination,
    Toolbar,
    ToolbarContent,
    MultiSelect,
  } from 'carbon-components-svelte';
  import { cloneDeep } from 'lodash';
  import { CustomTextInput } from 'src/components/common/input';
  import { CustomLoading } from 'src/components/common/loading';
  import DeleteUserModal from 'src/components/modal/DeleteUserModal.svelte';
  import AddNewUserModal from 'src/components/modal/AddNewUserModal.svelte';
  import { TextOverflowTooltip } from 'src/components/common/tooltip';
  import PermissionGuard from 'src/components/common/PermissionGuard.svelte';
  import { userListService } from 'src/service';
  import { formatQueryParams } from 'src/utils/appHelper.js';
  import { EMPTY } from 'src/constants/app.js';
  import { message } from 'src/store';
  import { PERMISSIONS } from 'src/utils/rolePermissions.js';
  import {
    USER_LIST_PAGE_SIZES,
    USER_LIST_HEADERS,
    USER_ROLES,
    USER_STATUSES,
    USER_LIST_FORM_OPTION,
    USER_TYPE_MAP,
    USER_STATUS_MAP,
  } from 'src/constants/userList.js';

  let formOption;
  let dataTable;
  let page = 1;
  let pageSize = USER_LIST_PAGE_SIZES[0];
  let isLoading = false;
  let userDelete = EMPTY;
  let userAdd = false;

  function openModalDeleteUser(user) {
    userDelete = user;
  }

  function openModalAddUser() {
    userAdd = true;
  }

  function sortByCreatedAt(data) {
    return data.sort((a, b) => {
      return new Date(b?.createdAt) - new Date(a?.createdAt);
    });
  }

  async function getDataUserList(formOption) {
    isLoading = true;
    page = 1;
    dataTable = [];

    const params = {
      limit: 60,
      paginationToken: null,
    };

    const payload = formatQueryParams(formOption, ['createdFrom', 'createdTo']);
    const response = await userListService.getUserList(params, payload);

    isLoading = false;

    const userData = sortByCreatedAt(response?.data?.users || []);
    dataTable = userData.map((user, index) => {
      return {
        id: (index + 1) * page,
        agencyId: user?.attributes?.['custom:agency_id'] || EMPTY,
        email: user?.attributes?.email || EMPTY,
        phoneNumber: user?.attributes?.phone_number || EMPTY,
        userType: USER_TYPE_MAP[user?.attributes?.['custom:role'] || EMPTY],
        status: USER_STATUS_MAP[user?.status || EMPTY],
        createdAt: user?.createdAt?.slice(0, 10) || EMPTY,
        updatedAt: user?.updatedAt?.slice(0, 10) || EMPTY,
        profile: user?.attributes?.profile || EMPTY,
        username: user?.username || EMPTY,
      };
    });

    if (!dataTable.length) {
      message.update(() => [
        {
          subtitle: '검색결과가 없습니다',
          caption: EMPTY,
        },
      ]);
    }
  }

  onMount(() => {
    formOption = cloneDeep(USER_LIST_FORM_OPTION);
    getDataUserList(formOption);
  });
</script>

{#if formOption}
  <div id="user-list-wrapper">
    <Form class="form-wrapper">
      <FormGroup>
        <DatePicker
          datePickerType="range"
          bind:valueFrom={formOption.createdFrom}
          bind:valueTo={formOption.createdTo}
          dateFormat="d/m/Y"
        >
          <DatePickerInput labelText="Created Date" placeholder="dd/mm/yyyy" />
          <DatePickerInput labelText=" " placeholder="dd/mm/yyyy" />
        </DatePicker>
      </FormGroup>
      <div class="m-l-32 w-190">
        <MultiSelect
          filterable
          titleText="Role"
          label="전체"
          placeholder="전체"
          items={USER_ROLES}
          bind:selectedIds={formOption.roles}
        />
      </div>
      <div class="m-l-32 w-160">
        <MultiSelect
          filterable
          titleText="Status"
          label="전체"
          placeholder="전체"
          items={USER_STATUSES}
          bind:selectedIds={formOption.statuses}
        />
      </div>
      <div class="m-l-32 w-320">
        <CustomTextInput labelText="Email" placeholder="Email" bind:value={formOption.email} />
      </div>
      <div class="wrapper-action">
        <PermissionGuard permissions={[PERMISSIONS.VIEW_USER_LIST, PERMISSIONS.CREATE_USER]}>
          <Button on:click={openModalAddUser}>신규 유저 등록</Button>
        </PermissionGuard>
        <PermissionGuard permission={PERMISSIONS.VIEW_USER_LIST}>
          <Button kind="secondary" on:click={() => getDataUserList(formOption)}>검색</Button>
        </PermissionGuard>
      </div>
    </Form>

    <div class="user-list-table">
      <DataTable headers={USER_LIST_HEADERS} rows={dataTable} {pageSize} {page} stickyHeader id="userList">
        <Toolbar class="table-toolbar">
          <ToolbarContent>
            <div class="title-table">
              <h4 class="title-table-content">검색결과</h4>
            </div>
          </ToolbarContent>
        </Toolbar>

        <svelte:fragment slot="cell" let:cell let:row>
          {#if ['email'].includes(cell.key)}
            <TextOverflowTooltip
              text={cell.value}
              class="color-outstand-cell"
              on:click={() => {
                openModalDeleteUser(row);
              }}
            >
              {cell.value}
            </TextOverflowTooltip>
          {:else}
            <TextOverflowTooltip text={cell.value}>
              {cell.value}
            </TextOverflowTooltip>
          {/if}
        </svelte:fragment>
      </DataTable>
      {#if dataTable?.length}
        <Pagination
          pageSizes={USER_LIST_PAGE_SIZES}
          bind:page
          totalItems={dataTable.length}
          bind:pageSize
          itemsPerPageText={EMPTY}
          itemRangeText={(min, max, total) => {
            return `${min} - ${max} of ${total} Users`;
          }}
        />
      {/if}
      {#if isLoading}
        <div class="loading-user-list">
          <CustomLoading />
        </div>
      {/if}
    </div>
  </div>
{/if}

<DeleteUserModal
  bind:userDelete
  on:delete-success={() => {
    userDelete = EMPTY;
    getDataUserList();
  }}
/>
<AddNewUserModal bind:userAdd on:success={() => getDataUserList()} />
