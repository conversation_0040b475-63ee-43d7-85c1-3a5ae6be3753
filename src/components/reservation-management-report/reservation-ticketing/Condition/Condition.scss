#reservation-ticketing-condition {
  .condition__form {
    display: flex;
    flex-direction: column;
    margin: 0;
    border-bottom: 1px solid #C6C6C6;
    padding-bottom: 16px;
  }

  .multi-way {
    .ffn-promtion-code-container {
      margin-left: 3rem;
    }

  }

  .condition__form-layout {
    display: flex;
    margin-top: 16px;
    .condition__form-layout-input {
      display: flex;
      gap: 16px;
      flex: 1;
      align-items: flex-start;
    }
    .bx--text-input__field-outer-wrapper {
      padding: 0;
      margin: 0;
      min-height: unset;
      height: unset;
    }
    .condition__form-layout-container {
      flex: 1;
      fieldset {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        gap: 4px;
      }
    }
    .condition__form-layout-input-container{
      flex: 1;
      display: flex;
      gap: 16px;
    }
  }
  .condition__box-promotion-code{
    // display: flex;
    // align-items: center;
    margin-top: 28px;
  }
  .destination-departure {
    display: flex;

    .bx--text-input__field-wrapper {
      width: 120px;
    }
  }

  .combobox {
    width: 180px;
  }
  .bx--multi-select {
    width: 180px;
  }
  .destination-departure__swap-button {
    background-color: transparent;
    border: none;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 22px 8px 0 8px;
    .destination-departure__swap-arrow-icon{
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      transform: scale(1.5);
    }

 
  }

  .condition__ticket {
    margin-top: 42px;
    display: flex;
  }

  .flight-schedule {
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: relative;

    .flight-schedule__item {
      display: flex;
      gap: 16px;
    }

    .flight-schedule__box-dateTime {
      display: flex;
      .flatpickr-wrapper {
        width: 143px;
      }
      input {
        width: 143px;
      }
    }

    .btn-toggle-wrapper {
      position: absolute;
      bottom: 8px;
      right: -32px;
      .btn-toggle {
        border: none;
        background-color: transparent;
        cursor: pointer;
        padding: 2px;
      }
    }
    .btn-toggle-wrapper--trick {
      right: -58px;
    }
  }

  .line {
    max-width: 1200px;
    border-top: 1px solid #cccccc;
  }

  .ffn-promtion-code-container {
    margin-top: 2rem;
    display: flex;
    flex-direction: column;
    gap: 16px;


    .ffn-container, .promotion-code-container {
      display: grid;
      gap: 16px;
      grid-template-columns: repeat(4,minmax(0,1fr));
      max-width: 900px;

      .ffn-btn, .promotion-code-btn {
        all: unset;
        font-size: 14px;
        color: #0F62FE;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 8px;
        cursor: pointer;
      }

      .ffn-wrapper, .promotion-code-wrapper {
        grid-column: span 3 / span 3;
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .ffn-list:empty, .promotion-code-list:empty {
        margin-top: calc(16px * -1);
      }

      .ffn-list, .promotion-code-list {
        display: grid;
        grid-template-columns: repeat(3,minmax(0,1fr));
        grid-column: span 3 / span 3;
        gap: 16px;

        .ffn-item, .promotion-code-item {
          display: grid;
          grid-template-columns: repeat(3,minmax(0,1fr));
          align-items: center;
          text-align: center;
          
          :nth-child(2) {
            grid-column: span 2 / span 2;
          }
        }
      }
    }

    .passenger-name {
      background-color: #0F62FE;
      border: 2px solid #0F62FE;
      padding: 2px 8px;
      color: #FFFFFF;
      p {
        font-size: 12px;
      }
    }

    .account-id {
      border: 1px solid #0F62FE;
      padding: 3px 16px;
      p {
        font-size: 12px;
      }
    }
  }
}

