<script>
  import Add from 'carbon-icons-svelte/lib/Add.svelte';
  import { createEventDispatcher } from 'svelte';
  export let text = 'none';

  const dispatch = createEventDispatcher();

  const handleClick = (event) => {
    dispatch('click', event);
  };
</script>

<button on:click|preventDefault|stopPropagation={handleClick}>
  {text}
  <span>
    <Add />
  </span>
</button>

<style>
  button {
    outline: none;
    border: none;
    background-color: transparent;
    color: #0f62fe;
    font-size: 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
    text-transform: capitalize;
    padding: 0;
    margin: 0;
  }
  span {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 4px;
  }
</style>
