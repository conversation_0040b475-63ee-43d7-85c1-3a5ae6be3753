$activeSelectColor:blue ;
$buttonActionColor:#002D9C;
$cardActiveColor:#d0e2ff;
$cardsContainerColor:#f4f4f4;

#flightTable {
  overflow: hidden;
  overflow-y: scroll;
  border: 1px solid var(--table-cell-border-color);
  position: relative;

  &::-webkit-scrollbar {
    width: 6px;
    height: 4px;
  }
  &::-webkit-scrollbar-track {
    background-color: #ebe8e8;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 20px;
  }

}


#flightTable table {
  width: 100%;
  border-collapse: separate;
  font-family: Arial, sans-serif;
  table-layout: fixed;
  position: relative;
  overflow: scroll;
  margin-bottom: 64px;

  thead {
    position: sticky;
    top: 0;
    z-index: 100;
    tr{
      margin-bottom: 2px !important;
    }
  }
 
  th,
  td {
    text-align: left;
    padding: 8px !important;
  }

  th {
    background-color: var(--table-header-color);
    font-weight: bold;
  }

  button {
    cursor: pointer;
    border: none;
    background-color: transparent;
  }
  td {
    vertical-align: middle;
    background-color: var(--table-cell-color);
    border-bottom: 1px solid var(--table-cell-border-color);
  }


  .flightTable__td--border-bottom-selected{
    border-bottom: 1px solid $activeSelectColor !important;
  }
  .flightTable__td--border-top-selected{
    border-top: 1px solid $activeSelectColor !important;
  }
  .flightTable__td--border-left-selected{
    border-left: 1px solid $activeSelectColor !important;

  }
  .flightTable__td--border-right-selected{
    border-right: 1px solid $activeSelectColor!important;
  }
  .flightTable__icon-ticket {
     padding: 0;
  }

  .flightTable__icon-arrow {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .flightTable__list-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 6px;
    height: 100%;
    position: relative;
  }
  .flightTable__list-airline-code {
    padding-left: 14px;
  }
  .flightTable__main-box-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .flightTable__icon-arrow.flightTable__icon-arrow--sub-row {
    position: absolute;
    left: -14px;
  }

  .flightTable__icon-arrow--collapse {
    visibility: collapse;
  }


  .flightTable__box-select {
    border-left: 1px solid var(--table-cell-border-color);
    border-right: 1px solid var(--table-cell-border-color);

    .flightTable__select-action {
      flex-direction: column;
      gap: 4px;
    }

    .select-action__layout {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .flightTable__select-button {
    width: 66px;
    padding: 6px 16px;
    border: 1px solid $activeSelectColor;
  }
  .flightTable__select-button--active{
    background-color: $buttonActionColor;
    color: white;
  }

  .flightTable__head-airline {
    width: 64px;
  }

  .flightTable__head-toggle-arrow {
    width: 84px;
  }

  .flightTable__td {
    position: relative;
  }

  .flightTable__td-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
  }

  .flightTable-row-logo {
    padding: 12px;
    display:  flex;
    flex-direction: column;
    gap: 8px;
    font-size: 12px;
    img {
      width: 40px;
      height: 40px;
      object-fit: contain;
      border: 1px solid var(--table-cell-border-color);
      padding: 4px;
      border-radius: 4px;
    }
  }

  .flightTable__cards-container {
    background-color: $cardsContainerColor;
    padding-top: 4px !important;
    padding-bottom: 4px !important;
    .flex {
      display: flex;
      gap: 8px;
    }
    .paxJourneyDetail {
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding-left: 16px;
    }
    .card-payment-info{
      display: flex;
      justify-content: center;
      gap: 4px;
    }
    ul {
      display: inline-flex;
      list-style-type: none;
      padding: 4px;
      margin: 0;
      display: flex;
      gap: 12px;
      overflow: hidden;
      overflow-x: scroll;

      &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #c4c4c4;
        border-radius: 20px;
      }
    }

    ul li {
      min-width: 340px;
      max-width: 340px;
      height: 140px;
      padding: 10px;
      border: 1px solid var(--table-cell-border-color);
      display: flex;
      flex-wrap: nowrap;
      border-radius: 4px;
      background-color: #ffffff;

      .flightTable__cards-wrapper{
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

    }
    ul li.active-selected-card{
         border: 1px solid  $activeSelectColor;
         background-color: $cardActiveColor;

    }
  }
}
