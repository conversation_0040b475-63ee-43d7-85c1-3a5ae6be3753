$borderColorCross: rgb(220, 212, 212);
$formPreviewColor: #ffffff;

.changeOfItinerary .itineraryInquiry {
  display: flex;
  min-height: 560px;
  height: inherit;
  .left-layout {
    padding: 0 8px;
    display: flex;
    flex-direction: column;
    height: 100%;
    h4 {
      font-size: 16px;
    }
    .layout-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      .content-switcher {
        button {
          height: 32px !important;
          width: 64px !important;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        button::after {
          height: 32px !important;
        }
      }
      .form-preview {
        background-color: $formPreviewColor;
        padding: 12px 16px;
        border-radius: 4px;
        max-width: 320px;
        .form-content {
          margin-top: 20px;
          height: 374px;
          overflow-y: scroll;
          h4 {
            padding: 12px 0;
            font-size: 16px;
          }
        }
        @include maxScreen {
          .form-content {
            height: unset;
            overflow-y: unset;
          }
        }
        .point_start-end {
          width: 288px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .input-point-start,
          .input-point-end {
            .bx--text-input {
              text-transform: uppercase;
            }
            width: 136px;
            height: 64px;
            input {
              height: 32px;
            }
          }
        }
        .departure-date {
          margin-top: 8px;
          .bx--date-picker {
            width: 288px;
          }
          input {
            width: 288px;
          }
          .bx--date-picker__calendar {
            position: absolute;
            top: 48px;
          }
        }
        .seat-class {
          margin-top: 16px;
          width: 200px;
        }
      }
      .check-btn {
        display: flex;
        justify-content: flex-end;
        align-items: end;
        height: 44px;
        button {
          width: 160px !important;
          height: 30px !important;
          min-width: unset !important;
          min-height: unset !important;
        }
      }
      .bx--content-switcher {
        justify-content: start;
        button {
          padding: 0px 8px;
          min-width: unset !important;
          min-height: unset !important;
          height: 36px;
          width: 74px;
        }
      }

      ul {
        border-top: 1px solid $borderColorCross;
        padding-top: 12px;
        padding-bottom: 4px;
        li {
          padding: 4px 0;
          display: flex;
          align-items: center;
          .existingItinerary-label {
            width: 100px;
          }
          .existingItinerary-value {
            width: 220px;
          }
        }
      }
    }
  }

  .right-layout {
    display: flex;
    flex-direction: column;
    height: auto;
    padding: 0 8px;
    margin-left: 16px;
    flex: 1;
    overflow-x: scroll;
    &::-webkit-scrollbar {
      width: 6px !important;
    }
    .layout-content {
      position: relative;
      flex: 1;
      margin-top: 4px;
      padding: 4px;
      border: 1px solid transparent;
      border-radius: 4px;
      overflow-y: scroll;
      .notify-result {
        color: rgba(168, 168, 168, 1);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 18px;
      }
      .loading-getdata-steptwo {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
    .layout-content.active-border {
      border-color: rgba(168, 168, 168, 1) !important;
    }
  }
  .right-layout .toolbar-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .info-date {
      display: flex;
      align-items: center;
      font-size: 16px;
      h4 {
        margin-right: 16px;
        font-size: 16px;
      }
      .info-date-item {
        .point-count {
          height: 12px;
          aspect-ratio: 1/1;
          border-radius: 50%;
          border: 1px solid black;
          font-size: 8px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        display: flex;
        align-items: center;
      }
      .info-date-item > div {
        display: flex;
        align-items: center;
        span {
          padding: 0 8px;
        }
      }
    }
    .Seat-class-color {
      display: flex;
      h4 {
        margin-right: 16px;
        font-size: 14px;
      }
      .box-class-color {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .seat-item {
        display: flex;
        align-items: center;
      }
      .seat-item-name {
        padding: 0 16px 0 4px;
        font-size: 14px;
      }
      .seat-item .circle {
        border-radius: 50%;
        height: 10px;
        width: 10px;
        background-color: var(--color-circle);
      }
    }
  }

  .right-layout .wrapperItineraryInquiry.active-border {
    border-bottom: 1px solid $borderColorCross;
  }
  .right-layout .wrapperItineraryInquiry.active-selected {
    border-color: rgba(15, 98, 254, 1) !important;
  }
  .right-layout .wrapperItineraryInquiry {
    border: 1px solid transparent;
    border-bottom-color: $borderColorCross;
    border-top-color: $borderColorCross;
    background-color: white;
    min-width: 1200px;
    display: flex;
    align-items: center;
    .box-item-itinerary {
      flex: 1;
      border-right: 1px solid $borderColorCross;
    }
    .box-selection-itinerary {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      padding: 8px;
      width: 200px;
      h4 {
        font-size: 18px;
        font-weight: 600;
      }
      button {
        margin-top: 8px;
        outline: unset;
        cursor: pointer;
        padding: 4px 20px;
        border: 1.6px solid;
        color: #0f62fe;
        font-size: 14px;
        background-color: white;
      }
      button.active-btn {
        background-color: #0f62fe;
        color: white;
      }
    }
  }

  .right-layout .item-ItineraryInquiry {
    display: flex;
    .controller {
      margin-top: 16px;
      width: 32px;
      display: flex;
      flex-direction: column;
      align-items: center;
      button {
        background-color: transparent;
        outline: unset;
        border: unset;
        cursor: pointer;
        transform: rotate(180deg);
        margin-top: 4px;
        transition: all 0s ease-in-out 1s;
      }
      button.show-full-list {
        transform: rotate(0);
      }
    }
    ul.active-border {
      border-bottom: 1px solid $borderColorCross;
    }
    ul {
      flex: 1;
      margin-left: 16px;
      li {
        position: relative;
        display: flex;
        align-items: center;
        background-color: white;
        margin-left: 24px;
        padding: 8px 0;
        height: 80px;
        .korea-image {
          position: absolute;
          left: -40px;
          top: 10px;
          width: 36px;
          aspect-ratio: 3/2;
          padding: 4px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .cross-path-y {
          height: 100%;
          width: 1px;
          background-color: $borderColorCross;
        }
        .space-white {
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          height: 100%;
        }
        .time-trip {
          font-weight: 500;
          font-size: 20px;
        }
        .point-trip {
          font-size: 16px;
        }
        .korean-air {
          width: 120px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          &-id {
            font-size: 16px;
          }
        }
        .location-airport {
          width: 80px;
        }
        .rbd-space {
          padding-top: 6px;
          width: 146px;
          padding-left: 20px;
          display: inline-block;
          .rbd-code {
            background-color: var(--bg-cabinCode);
            border-radius: 12px;
            margin-top: 12px;
            padding: 2px 12px;
            margin-left: -2px;
            display: inline-flex;
          }
          .group-main-circle {
            display: flex;
            margin-top: 20px;
          }
          .rbd-circle {
            border-radius: 50%;
            background-color: var(--bg-cabinCode-circle);
            width: 12px;
            height: 12px;
          }
          .rbd-circle + .rbd-circle {
            margin-left: 8px;
          }
        }
        .tag-text {
          width: 200px;
        }
        .baggage-support {
          display: flex;
          flex-direction: column-reverse;
          height: 100%;
          padding: 8px;
        }
        .air-name {
          width: 120px;
        }
      }
      li.visible-border-top {
        border-top: 1px solid $borderColorCross;
      }
      li .flightRoute {
        width: 132px;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        margin-right: 20px;
      }
    }
  }
}
