<script>
  import { createEventDispatcher } from 'svelte';
  import RuleCoveredServicesModal from 'src/components/modal/RuleCoveredServicesModal.svelte';
  import Ticket from 'carbon-icons-svelte/lib/Ticket.svelte';
  import { has, isEqual, uniqWith, isEmpty } from 'lodash';
  import { v4 as uuidv4 } from 'uuid';
  import { DataTable, Toolbar, ToolbarContent, Button, InlineLoading } from 'carbon-components-svelte';
  import { _ } from 'src/lib/i18n.js';
  import { EMPTY, CODE_SUCCESS, AIRLINES_GROUP_BUY_SERVICES_AIRSHOPPING } from 'src/constants/app.js';
  import { formatPriceNumber, sortPaxListByPtc, getPassengerNamePtc } from 'src/utils/appHelper.js';
  import { QuestionTooltip } from 'src/assets/icons';
  import { screenIsNotEvent } from 'src/store';
  import { getServiceList } from 'src/service/reservationTicketing.js';
  import BaggageInformationAirShopping from 'src/components/modal/baggage-information-modal/BaggageInformationAirShopping.svelte';
  import TaxBreakdownModal from 'src/components/modal/TaxBreakdownModal.svelte';
  import { generatePopupCoveredServices, splitConvertArrayId, formatInfoSegment } from 'src/utils/appHelper';
  import PaymentOrIssueButton from 'src/components/common/button/PaymentOrIssueButton.svelte';
  import { PRIME_BOOKING_SERVICE_WHITE_LIST } from 'src/constants/baggageService';
  import PermissionGuard from 'src/components/common/PermissionGuard.svelte';
  import { PERMISSIONS } from 'src/utils/rolePermissions';

  export let DataLists = {};
  export let Order = {};
  export let PricedOffer = {};
  export let RepricedOffer = {};
  export let orderRetrieve = {};
  export let orderId = EMPTY;
  export let airlineID = EMPTY;
  export let isPayLater = false;
  export let pnr = EMPTY;
  export let isPrimeBooking = false;

  const dispatch = createEventDispatcher();
  const headers = [
    {
      key: 'passengerNamePtc',
      value: $_('layout.pages.reservationManagement.amountInformationTable.dataTable.headers.passengerNamePtc', {
        default: '탑승객명 (PTC)',
      }),
    },
    {
      key: 'item',
      value: $_('layout.pages.reservationManagement.amountInformationTable.dataTable.headers.item', {
        default: 'Item',
      }),
    },
    {
      key: 'segments',
      value: $_('layout.pages.reservationManagement.amountInformationTable.dataTable.headers.segments', {
        default: 'Segments',
      }),
    },
    {
      key: 'baseAmount',
      value: $_('layout.pages.reservationManagement.amountInformationTable.dataTable.headers.baseAmount', {
        default: 'BaseAmount',
      }),
    },
    {
      key: 'totalTax',
      value: $_('layout.pages.reservationManagement.amountInformationTable.dataTable.headers.totalTax', {
        default: 'TotalTax',
      }),
    },
    {
      key: 'totalFare',
      value: $_('layout.pages.reservationManagement.amountInformationTable.dataTable.headers.totalFare', {
        default: 'TotalFare',
      }),
    },
    {
      key: 'actionTicket',
      value: EMPTY,
    },
  ];
  let isPayLaterButtonDisable = false;

  let rows = [];
  let serviceRows = [];
  let openTaxBreakDown = false;
  let taxBreakDownData = [];
  let openRuleCoveredServicesModal = false;
  let ruleCoveredServicesModalData = {
    headers: [],
    rows: [],
  };
  let totalFareAll = 0;
  let curCode = EMPTY;

  let serviceList = {};
  let isLoadingServiceList = false;
  let openModalBaggage = false;
  let baggageOrderRetrieve = {
    data: {
      DataLists,
      PricedOffer,
      ResultMessage: {
        Code: CODE_SUCCESS,
      },
    },
  };
  let dataBuyService = {};

  async function openBaggageModal() {
    // For Prime booking
    try {
      const { ResponseID = EMPTY, OfferID = EMPTY, Owner = EMPTY } = PricedOffer;

      isLoadingServiceList = true;
      screenIsNotEvent.set(true);

      const payload = { Offer: { ResponseID, OfferID, Owner } };

      const response = await getServiceList(payload);
      if (response?.data?.ResultMessage?.Code === CODE_SUCCESS) {
        serviceList = response;
        openModalBaggage = true;
      }
    } catch (error) {
      console.log(error);
    } finally {
      isLoadingServiceList = false;
      screenIsNotEvent.set(false);
    }
  }

  function getInfoAmountServiceChooseInServiceList(UnitPriceDetail) {
    const data = {
      baseAmount: EMPTY,
      totalTax: EMPTY,
      totalFare: EMPTY,
      taxData: [],
      actionTicket: {
        headers: [],
        rows: [],
      },
    };
    if (UnitPriceDetail) {
      data.baseAmount = UnitPriceDetail.TotalBaseAmount;
      data.totalTax = UnitPriceDetail.TotalTaxAmount;
      data.totalFare = {
        Amount: UnitPriceDetail.TotalAmount.Amount + UnitPriceDetail.TotalTaxAmount.Amount,
        CurCode: UnitPriceDetail.TotalBaseAmount.CurCode,
      };
      totalFareAll += data.totalFare.Amount;
      curCode = data.totalFare.CurCode;
    }
    return data;
  }

  function getFareInformation(FareDetail, options, isOnlyService = false) {
    const { PaxSegmentList, JourneyOverview, PaxJourneyList, Price } = options;
    const fare = {
      baseAmount: EMPTY,
      totalTax: EMPTY,
      totalFare: EMPTY,
      taxData: [],
      actionTicket: {
        headers: [],
        rows: [],
      },
    };

    if (FareDetail) {
      const { BaseAmount, TaxTotal, Tax } = FareDetail;
      fare.baseAmount = BaseAmount;
      fare.totalTax = TaxTotal;
      fare.totalFare = {
        Amount: BaseAmount?.Amount + TaxTotal.Amount,
        CurCode: BaseAmount.CurCode,
      };
      fare.taxData = Tax.map((taxItem) => ({
        TaxCode: taxItem.TaxCode,
        Amount: taxItem.Amount.Amount,
        CurCode: taxItem.Amount.CurCode,
      }));

      if (!isOnlyService) {
        // Only ticket has this
        fare.actionTicket = generatePopupCoveredServices(
          PaxSegmentList,
          JourneyOverview,
          PaxJourneyList,
          FareDetail?.FareComponent ?? [],
          airlineID
        );
      }
      totalFareAll += fare.totalFare.Amount;
      curCode = fare.totalFare.CurCode;
    } else if (isOnlyService) {
      const TotalAmount = Price?.TotalAmount;
      fare.totalFare = {
        Amount: TotalAmount?.Amount,
        CurCode: TotalAmount.CurCode,
      };
      totalFareAll += TotalAmount.Amount;
      curCode = TotalAmount.CurCode;
    }

    return fare;
  }

  $: if (
    !isEmpty(DataLists) &&
    ((!isEmpty(Order) && has(Order, 'OrderItem')) ||
      (!isEmpty(PricedOffer) && has(PricedOffer, 'OfferItem')) ||
      (!isEmpty(RepricedOffer) && has(RepricedOffer, 'RepricedOfferItem')))
  ) {
    const { PaxList, PaxJourneyList, PaxSegmentList } = DataLists;
    let ServiceList = [];
    totalFareAll = 0;
    curCode = EMPTY;
    rows = [];
    const OrderOrOfferItem = RepricedOffer?.RepricedOfferItem ?? PricedOffer?.OfferItem ?? Order?.OrderItem ?? [];
    const JourneyOverview = RepricedOffer?.JourneyOverview ?? PricedOffer?.JourneyOverview ?? Order?.JourneyOverview;
    for (const { Service, OfferItemID } of OrderOrOfferItem) {
      ServiceList = [
        ...ServiceList,
        ...Service.map((elm) => {
          return {
            ...elm,
            OfferItemID,
          };
        }),
      ];
    }
    ServiceList = uniqWith(ServiceList, isEqual);

    // New logic loop offerItems
    for (const offerItem of OrderOrOfferItem) {
      const PaxRefID =
        offerItem?.PaxRefID.length > 0
          ? offerItem?.PaxRefID
          : offerItem.FareDetail.reduce((acc, item) => {
              acc = [...acc, ...item.PaxRefID];
              return acc;
            }, []);
      const rowItemsOfPax = PaxRefID.map((PaxID) => {
        const paxInfo = PaxList.find((pax) => pax.PaxID === PaxID);
        const { Individual, Ptc } = paxInfo;
        let passengerNamePtc = EMPTY,
          item = [],
          segments = [];
        const ServiceListByPaxID = ServiceList.filter((it) => splitConvertArrayId(it.PaxRefID).includes(PaxID));
        const FareDetail = offerItem?.FareDetail?.find((FareDetailItem) =>
          splitConvertArrayId(FareDetailItem.PaxRefID).includes(PaxID)
        );
        const ServiceListByPaxIDCheckDefinition = ServiceListByPaxID.filter((it) => has(it, 'Definition'));
        passengerNamePtc = getPassengerNamePtc({ Individual, Ptc, PaxID });
        if (
          ServiceListByPaxID.length !== ServiceListByPaxIDCheckDefinition.length ||
          ServiceListByPaxIDCheckDefinition.length !== 0 ||
          ServiceListByPaxID.length === 0
        ) {
          item = ['FLIGHT'];
        } else {
          if (ServiceListByPaxIDCheckDefinition.length > 0) {
            const { SelectedSeat, Definition } = ServiceListByPaxIDCheckDefinition[0];
            if (SelectedSeat) {
              item.push(`${Definition.Name}/${SelectedSeat.Row}/${SelectedSeat.Column}`);
            }
            if (Definition.Desc?.length === 1) {
              item.push(`${Definition.Name}/${Definition.Desc[0]?.Text}`);
            }
          }
        }

        if (isEqual(item, ['FLIGHT'])) {
          let PaxSegmentRefIDList = [];
          for (const fareDetail of offerItem.FareDetail) {
            PaxSegmentRefIDList = [
              ...PaxSegmentRefIDList,
              ...fareDetail.FareComponent.reduce((acc, item) => {
                acc = [...acc, ...item.PaxSegmentRefID];
                return acc;
              }, []),
            ];
          }
          for (const PaxSegmentRefIDItem of PaxSegmentRefIDList) {
            const paxSegment = PaxSegmentList.find(
              (PaxSegmentListItem) => PaxSegmentListItem.PaxSegmentID === PaxSegmentRefIDItem
            );
            if (paxSegment) {
              segments.push(formatInfoSegment(paxSegment));
            }
          }
        }

        const fareInfo = getFareInformation(FareDetail, { PaxSegmentList, JourneyOverview, PaxJourneyList });
        return {
          id: uuidv4(),
          passengerNamePtc,
          item,
          segments,
          Ptc,
          ...fareInfo,
        };
      });

      rows = sortPaxListByPtc([...rows, ...rowItemsOfPax]);
    }

    if (dataBuyService?.selectedServices?.length) {
      serviceRows = [];
      const { PaxList, PaxSegmentList } = DataLists;

      for (const service of dataBuyService.selectedServices) {
        const { Service, Price = {} } = service;
        const { PaxRefID = EMPTY, Definition = {}, PaxSegmentRefID = EMPTY } = Service?.[0] || {};

        let passengerNamePtc = EMPTY,
          item = [],
          segments = [];
        item = [`${Definition?.Name || EMPTY}`];
        const sortPaxList = sortPaxListByPtc(PaxList);
        const paxItemIdx = sortPaxList.findIndex((pax) => pax.PaxID === PaxRefID);
        passengerNamePtc = `탑승객 ${paxItemIdx + 1} ${sortPaxList[paxItemIdx]?.Ptc || EMPTY}`;

        const paxSegment = PaxSegmentList.find(
          (PaxSegmentListItem) => PaxSegmentListItem.PaxSegmentID === PaxSegmentRefID
        );

        if (paxSegment) {
          segments = [...segments, formatInfoSegment(paxSegment)];
        }

        let infoAmountService = {};
        if (AIRLINES_GROUP_BUY_SERVICES_AIRSHOPPING.GROUP_1.includes(airlineID)) {
          const FareDetail = service?.FareDetail?.find((FareDetailItem) =>
            splitConvertArrayId(FareDetailItem.PaxRefID).includes(PaxRefID)
          );
          infoAmountService = getFareInformation(FareDetail, { Price }, true);
        }
        if (AIRLINES_GROUP_BUY_SERVICES_AIRSHOPPING.GROUP_2.includes(airlineID)) {
          infoAmountService = getInfoAmountServiceChooseInServiceList(service.UnitPriceDetail);
        }
        serviceRows = [
          ...serviceRows,
          {
            id: uuidv4(),
            passengerNamePtc,
            item,
            segments,
            ...infoAmountService,
            OfferItemID: service.OfferItemID,
          },
        ];
      }

      serviceRows = serviceRows.sort((a, b) => a.passengerNamePtc.localeCompare(b.passengerNamePtc));

      dispatch('update-new-service', {
        Order: dataBuyService.Order,
        TotalPrice: {
          TotalAmount: {
            Amount: totalFareAll,
            CurCode: curCode,
          },
        },
      });
    }
  }
  $: if (!isEmpty(Order)) {
    const now = new Date();
    isPayLaterButtonDisable = Order.OrderItem?.[0]?.['PaymentTimeLimit']
      ? now > new Date(Order.OrderItem[0]['PaymentTimeLimit'])
      : false;
  }
</script>

<DataTable {headers} rows={rows.concat(serviceRows)} id="table-management" class="g-custom-table" size="medium">
  <Toolbar size="sm" class="table-toolbar">
    <ToolbarContent>
      <div class="wrapper-title">
        <h4 class="title-content">결제 대상 금액 정보</h4>
      </div>
      <div class="wrapper-actions-button">
        {#if isPrimeBooking && PRIME_BOOKING_SERVICE_WHITE_LIST.includes(airlineID)}
          <PermissionGuard permission={PERMISSIONS.PURCHASE_SERVICE}>
            <Button size="small" kind="secondary" on:click={openBaggageModal}>
              {#if isLoadingServiceList}
                <div class="g-custom-loading-button loading-box">
                  <InlineLoading description="Loading" />
                </div>
              {:else}
                {$_('layout.pages.reservationManagement.itineraryInformationTable.toolbar.buttonService', {
                  default: '서비스',
                })}
              {/if}
            </Button>
          </PermissionGuard>
        {/if}
        {#if isPayLater}
          <PaymentOrIssueButton
            {isPayLaterButtonDisable}
            {DataLists}
            {PricedOffer}
            {Order}
            {orderRetrieve}
            {orderId}
            {airlineID}
            {pnr}
          />
        {/if}
      </div>
    </ToolbarContent>
  </Toolbar>
  <div slot="cell" let:cell let:row>
    {#if ['item', 'segments'].includes(cell.key)}
      <ul>
        {#each cell.value as valueItem}
          <li>
            {valueItem}
          </li>
        {/each}
      </ul>
    {:else if ['baseAmount', 'totalTax', 'totalFare'].includes(cell.key)}
      <div class="flex">
        <div>
          {formatPriceNumber(cell.value?.Amount ?? EMPTY)}
          {cell.value?.CurCode ?? EMPTY}
        </div>
        {#if cell.value.Amount > 0 && cell.key === 'totalTax' && row.taxData.length > 0}
          <button
            class="g-button question-icon"
            on:click={() => {
              (openTaxBreakDown = true), (taxBreakDownData = row.taxData);
            }}
          >
            <QuestionTooltip />
          </button>
        {/if}
      </div>
    {:else if cell.key === 'actionTicket'}
      {#if cell.value.headers.length > 0 || cell.value.rows.length > 0}
        <button
          class="g-button"
          on:click={() => {
            openRuleCoveredServicesModal = true;
            ruleCoveredServicesModalData = cell.value;
          }}
        >
          <Ticket />
        </button>
      {/if}
    {:else}
      {cell.value}
    {/if}
  </div>
</DataTable>
<div class="grand-total-fare">
  Grand Total Fare : {`${formatPriceNumber(totalFareAll)} ${curCode ?? EMPTY}`}
</div>
<TaxBreakdownModal bind:open={openTaxBreakDown} data={taxBreakDownData} />
<RuleCoveredServicesModal
  bind:open={openRuleCoveredServicesModal}
  rows={ruleCoveredServicesModalData.rows}
  headers={ruleCoveredServicesModalData.headers}
/>

{#key serviceList}
  <BaggageInformationAirShopping
    idBox="box-modal-children"
    bind:open={openModalBaggage}
    orderRetrieve={baggageOrderRetrieve}
    on:submit-selected-services={(event) => {
      dataBuyService = event.detail;
    }}
    {airlineID}
    {serviceList}
    {orderId}
  />
{/key}

<style>
  .flex {
    display: flex;
    align-items: center;
  }
  .question-icon {
    display: flex;
    width: auto !important;
    justify-content: center;
    align-items: center;
  }
  .grand-total-fare {
    background: #e0e0e0;
    text-align: right;
    padding: 5px 10px;
    font-size: 16px;
    font-weight: bold;
  }
</style>
