<script>
  import { user } from 'src/store';
  import { hasPermission, hasAnyPermission, hasAllPermissions, isAdmin } from 'src/utils/rolePermissions.js';

  // Props
  export let permission = null;
  export let permissions = [];
  export let requireAll = false;
  export let adminOnly = false;
  export let fallback = null;
  export let showFallback = false;

  $: userRole = $user?.role;

  $: hasRequiredPermission = (() => {
    if (!userRole) return false;

    if (adminOnly) {
      return isAdmin(userRole);
    }

    if (permission) {
      return hasPermission(userRole, permission);
    }

    if (permissions && permissions.length > 0) {
      return requireAll ? hasAllPermissions(userRole, permissions) : hasAnyPermission(userRole, permissions);
    }

    return true;
  })();
</script>

{#if hasRequiredPermission}
  <slot />
{:else if showFallback && fallback}
  <svelte:component this={fallback} />
{:else if showFallback}
  <slot name="fallback">
    <div class="permission-denied">
      <p>이 기능을 사용할 수 없습니다.</p>
    </div>
  </slot>
{/if}

<style>
  .permission-denied {
    padding: 1rem;
    text-align: center;
    color: #666;
    font-style: italic;
  }
</style>
