<script>
  import { createEventDispatcher } from 'svelte';
  import { PasswordInput } from 'carbon-components-svelte';
  import { CloseCircle, TickCircle } from 'src/assets/icons';

  export let value;

  const dispatch = createEventDispatcher();
</script>

<PasswordInput
  {...$$props}
  bind:value
  on:input={(e) => dispatch('input', e)}
  on:focus={() => dispatch('focus')}
  on:blur={() => dispatch('blur')}
/>

<div class="input-wrapper-svg">
  <CloseCircle />
  <TickCircle />
</div>
