<script>
  import { MultiSelect } from 'carbon-components-svelte';
  import { onMount } from 'svelte';
  import { getAgencyProviderAirlineCode } from 'src/service/reservation.js';

  export let selectedIdsAirlineId = [];
  export let airlineCode = [];
  onMount(async () => {
    const res = await getAgencyProviderAirlineCode();
    const airlineCodeApi = res.map((it, index) => {
      const { AirlineCode } = it;
      return {
        id: AirlineCode,
        text: AirlineCode,
      };
    });
    airlineCode = [...airlineCodeApi];
  });
</script>

<MultiSelect
  filterable
  titleText="항공사"
  label="전체"
  placeholder="전체"
  items={airlineCode}
  sortItem={(a, b) => a.id - b.id}
  bind:selectedIds={selectedIdsAirlineId}
/>
