<script>
  import PnrChangeHistory from 'src/components/modal/PnrChangeHistory.svelte';
  import { Clock } from 'src/assets/icons';
  import { getPnrChangeHistory } from 'src/service/reservation';
  import { screenIsNotEvent } from 'src/store';

  export let selectedTab;

  let openPnrChangeHistory = false;
  let pnrChangeHistoryData = [];

  async function openPnrChangeHistoryModal() {
    screenIsNotEvent.set(true);
    const params = {
      order_id: selectedTab.orderId,
      booking_reference: selectedTab.tabName,
      airline_id: selectedTab.airlineId,
    };

    const resGetPnrChangeHistory = await getPnrChangeHistory(params);
    const { code, data } = resGetPnrChangeHistory || {};
    if (code === 200) {
      pnrChangeHistoryData = data?.Response?.HistoryData || [];
      openPnrChangeHistory = true;
    }
    screenIsNotEvent.set(false);
  }
</script>

<button class="btn-raw" on:click={openPnrChangeHistoryModal}>
  <Clock />
</button>

{#key pnrChangeHistoryData}
  <PnrChangeHistory
    bind:open={openPnrChangeHistory}
    {pnrChangeHistoryData}
    pnr={selectedTab?.tabName}
    airlineId={selectedTab?.airlineId}
    orderId={selectedTab?.orderId}
  />
{/key}
