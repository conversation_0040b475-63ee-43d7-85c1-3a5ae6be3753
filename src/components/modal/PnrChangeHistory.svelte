<script>
  import { onMount } from 'svelte';
  import { v4 as uuidv4 } from 'uuid';
  import { Modal, DataTable } from 'carbon-components-svelte';
  import { EMPTY } from 'src/constants/app.js';
  import 'src/styles/modal/pnr-change-history.scss';

  export let open;
  export let pnrChangeHistoryData;
  export let pnr = EMPTY;
  export let airlineId = EMPTY;
  export let orderId = EMPTY;

  let dataTable = [];

  const headers = [
    {
      key: 'changeDateTime',
      value: 'Date/Time (KST)',
    },
    {
      key: 'changeOwner',
      value: 'Site',
      sort: false,
    },
    {
      key: 'userID',
      value: 'User',
      sort: false,
    },
    {
      key: 'userRole',
      value: 'UserRole',
      sort: false,
    },
    {
      key: 'changeType',
      value: 'Change Type',
      sort: false,
    },
    {
      key: 'detail',
      value: 'Details',
      sort: false,
    },
  ];

  onMount(() => {
    dataTable = pnrChangeHistoryData.map((item) => ({
      id: uuidv4(),
      changeDateTime: item.ChangeDateTime,
      changeOwner: item.ChangeOwner,
      userID: item.UserID,
      userRole: item.UserRole,
      changeType: item.ChangeType,
      detail: item.Detail,
    }));
  });
</script>

<Modal id="modal-pnr-change-history" modalHeading={`PNR History: ${pnr}`} bind:open size="lg">
  <div class="modal-header">
    <p>{airlineId}</p>
    <p>•</p>
    <p>Order ID: {orderId}</p>
  </div>

  <DataTable
    sortable
    stickyHeader={true}
    {headers}
    rows={dataTable}
    sortKey="changeDateTime"
    sortDirection="descending"
  >
    <div slot="cell" let:cell let:row style="text-align: center;">
      {#if cell.key === 'changeDateTime'}
        <p class="change-date-time">{cell.value}</p>
      {:else if cell.key === 'changeType'}
        <strong>{cell.value}</strong>
      {:else if cell.key === 'detail'}
        <p class="detail-text">{cell.value}</p>
      {:else}
        <p>{cell.value}</p>
      {/if}
    </div>
  </DataTable>
</Modal>
