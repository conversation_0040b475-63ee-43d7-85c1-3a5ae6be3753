<script>
  import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, But<PERSON> } from 'carbon-components-svelte';
  import { PrimaryButton } from 'src/components/common/button';
</script>

<div class="g-custom-table">
  <Toolbar size="sm">
    <ToolbarContent>
      <div class="table-toolbar">
        <div class="wrapper-title">
          <h4 class="title-content">수하물</h4>
        </div>
        <div class="wrapper-actions-button">
          <Button kind="secondary" size="small">수하물</Button>
        </div>
      </div>
    </ToolbarContent>
  </Toolbar>
</div>
<div class="baggageManageTable">
  <div class="no-selected-notify">추가한 수하물이 없습니다</div>
  <!-- <div class="selected-action">
    <PrimaryButton kind="secondary">수하물</PrimaryButton>
  </div> -->
</div>
