<script>
  import { isEmpty, cloneDeep } from 'lodash';
  import { EMPTY } from 'src/constants/app.js';
  import PassengerInformationItem from './PassengerInformationItem.svelte';
  import { createEventDispatcher } from 'svelte';
  import { sortPaxListByPtc } from 'src/utils/appHelper';
  import { Toolbar, ToolbarContent } from 'carbon-components-svelte';
  export let PaxList = [];
  export let arline = EMPTY;
  let allPassengerItemValue = {};
  let refPassengerInformationItems = {};
  let representativeContactInformationPaxId = EMPTY;
  let contactRefusalList = [];
  const dispatch = createEventDispatcher();
  const handleChangeValues = (refAllPassengerItemValue, mainContactInformationPaxId, refContactRefusalList) => {
    if (isEmpty(refAllPassengerItemValue)) return;
    let PaxList = [];
    let ContactInfoList = [];
    let INFmapping = 0;
    // generate default
    for (const key in refAllPassengerItemValue) {
      const { paxListItem, contactInfoListItem } = refAllPassengerItemValue[key];
      PaxList.push(paxListItem);
      if (['AF', 'KL'].includes(arline) && contactInfoListItem.ContactRefusedInd) {
        // case ContactRefusedInd=true ,delete property EmailAddress,Phone
        delete contactInfoListItem['EmailAddress'];
        delete contactInfoListItem['Phone'];
      }
      ContactInfoList.push(contactInfoListItem);
    }

    ContactInfoList = mappingContactInfoListByPaxRefId(ContactInfoList, PaxList);

    if (mainContactInformationPaxId !== EMPTY) {
      // choice representative contact information
      const targetPaxItem = PaxList.find((paxItem) => paxItem.PaxID === mainContactInformationPaxId);
      const targetContactInfo = ContactInfoList.find(
        (contact) => contact?.ContactInfoID === targetPaxItem?.ContactInfoRefID?.[0]
      );
      ContactInfoList = mappingContactInfoList(ContactInfoList, targetContactInfo);
      PaxList = PaxList.map((paxItem, index) => ({ ...paxItem, ContactInfoRefID: [`CI${index}`] }));

      if (['AF', 'KL'].includes(arline)) {
        if (!refContactRefusalList.includes(mainContactInformationPaxId)) {
          for (const refusalPaxId of refContactRefusalList) {
            const contactInfoRefID = PaxList.find((pax) => pax.PaxID === refusalPaxId)?.ContactInfoRefID?.[0] || EMPTY;
            ContactInfoList = mappingContactInfoListWithRefusal(ContactInfoList, contactInfoRefID);
          }
        }
      }
    }

    dispatch('change', { PaxList, ContactInfoList });
  };

  function mappingContactInfoListByPaxRefId(rawContactInfoList, paxList) {
    const clonedContactInfoList = cloneDeep(rawContactInfoList);
    const clonedPaxList = cloneDeep(paxList);
    const contactInfoList = [];

    for (const item of clonedContactInfoList) {
      let contactInfo = cloneDeep(item);
      const paxItem = clonedPaxList.find((pax) => pax?.ContactInfoRefID?.[0] === contactInfo?.ContactInfoID);

      // Equal case is INF -> Using ContactInfo of ADT
      if (paxItem?.PaxRefID) {
        const paxInfoOfADT = clonedPaxList.find((pax) => pax?.PaxID === paxItem?.PaxRefID);
        const contactInfoOfADTByPaxRefId = clonedContactInfoList.find(
          (contact) => contact?.ContactInfoID === paxInfoOfADT?.ContactInfoRefID?.[0]
        );
        contactInfo = {
          ...contactInfoOfADTByPaxRefId,
          ContactInfoID: contactInfo?.ContactInfoID,
        };
      }

      contactInfoList.push(contactInfo);
    }

    return contactInfoList;
  }

  function mappingContactInfoListWithRefusal(rawContactInfoList, contactInfoRefID) {
    const contactInfoList = cloneDeep(rawContactInfoList);

    return contactInfoList.map((contactInfo) => {
      const { ContactInfoID } = contactInfo;
      if (contactInfoRefID === ContactInfoID)
        return {
          ContactInfoID,
          ContactRefusedInd: true,
        };

      return contactInfo;
    });
  }

  function mappingContactInfoList(rawContactInfoList, targetContactInfo = {}) {
    const contactInfoList = cloneDeep(rawContactInfoList);
    return contactInfoList.map((_, index) => {
      return {
        ...targetContactInfo,
        ContactInfoID: `CI${index}`,
      };
    });
  }

  export const checkInValid = () => {
    let check = false;
    for (const key in refPassengerInformationItems) {
      if (refPassengerInformationItems[key]?.getInvalidList().length > 0) check = true;
    }
    return check;
  };

  $: handleChangeValues(allPassengerItemValue, representativeContactInformationPaxId, contactRefusalList);
</script>

<div class="g-custom-table">
  <Toolbar size="sm">
    <ToolbarContent>
      <div class="table-toolbar">
        <div class="wrapper-title">
          <h4 class="title-content">탑승객 정보</h4>
        </div>
        <div class="wapper-actions-button" />
      </div>
    </ToolbarContent>
  </Toolbar>
</div>
<div class="passenger-information">
  {#each sortPaxListByPtc(PaxList) as item, index (index)}
    <div class="passenger-information__box">
      <PassengerInformationItem
        bind:this={refPassengerInformationItems[item.PaxID]}
        bind:representativeContactInformationPaxId
        bind:passengerInformationItem={allPassengerItemValue[item.PaxID]}
        bind:contactRefusalList
        paxList={sortPaxListByPtc(PaxList)}
        paxItem={item}
        {index}
        {arline}
      />
    </div>
  {/each}
</div>
