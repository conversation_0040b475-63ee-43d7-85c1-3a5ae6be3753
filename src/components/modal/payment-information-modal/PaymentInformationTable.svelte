<script>
  import { isEmpty } from 'lodash';
  import { EMPTY } from 'src/constants/app.js';
  import { RadioButton, DataTable, Toolbar, ToolbarContent, Button, InlineLoading } from 'carbon-components-svelte';
  import { createEventDispatcher, onMount } from 'svelte';
  import { CustomTextInput } from 'src/components/common/input';
  import CreditCard from 'src/components/modal/payment-information-modal/CreditCard.svelte';
  import { postOrderChange } from 'src/service/reservation';
  import { user } from 'src/store';
  import { hasPermission, PERMISSIONS, ROLES } from 'src/utils/rolePermissions';
  export let pricedOffer = {};
  export let Order = {};
  export let RepricedOffer = {};
  export let TotalPrice = {}; // This amount is the cost when purchasing additional services at the time of creating the PNR from the AirShopping screen --> Normally, other screens do not have this -->
  /*Note:
  The typePayInformation has two possible values: createPrice and rePrice
  createPrice: Indicates booking a ticket from the AirShopping screen
  rePrice: Appears on the PNR detail screen when the ticket has been created (PNR) but not yet paid for, and the payment modal is reopened from the PNR detail screen */
  export let typePayInformation = 'createPrice';
  export let checkedValue = EMPTY;
  export let OrderID;
  export let statusCheckOrderChange = 'NONE';
  export let airlineId = EMPTY;
  const OfferID = pricedOffer.OfferID ?? Order.OfferID ?? RepricedOffer.OfferID ?? EMPTY;
  const headers = [
    {
      key: 'head',
      value: '결제방식',
      width: '400px',
    },
    {
      key: 'action',
      value: '결제수단',
    },
  ];
  let isOpenCreditCard = false;
  let infoCardCredit = EMPTY;

  let rows = [
    {
      id: 1,
      head: '즉시결제',
      action: null,
    },
  ];

  const dispatch = createEventDispatcher();

  const handleChangeCheckedValue = (currentValue) => {
    checkedValue = checkedValue === currentValue ? EMPTY : currentValue;
  };

  async function handleFirstOrderChange() {
    try {
      statusCheckOrderChange = 'CALLING_ORDER_CHANGE';
      await postOrderChange({
        query: {
          OrderID: OrderID,
          ChangeOrderChoice: {
            AcceptRepricedOrder: {
              OfferRefID: [OfferID],
            },
            UpdatePax: [],
          },
          PaxList: [],
          ContactInfoList: [],
          PaymentList: [],
        },
      });
      statusCheckOrderChange = 'CALL_ORDER_CHANGE';
    } catch (error) {
      statusCheckOrderChange = 'NONE';
    }
  }

  $: if (typePayInformation !== EMPTY) {
    let PaymentList = [];
    if (['cash', 'voucher', 'cash_voucher', 'agt', 'credit_card'].includes(checkedValue)) {
      let source = null;
      let orderItemIDs = [];

      if (!isEmpty(pricedOffer)) {
        source = pricedOffer;
      } else if (!isEmpty(Order)) {
        source = Order;
        orderItemIDs = Order?.OrderItem?.map((item) => item.OrderItemID) || [];
      } else if (!isEmpty(RepricedOffer)) {
        source = RepricedOffer;
      }

      const typeMap = { agt: 'Ag', cash: 'Cash', credit_card: 'Card' };
      const Type = typeMap[checkedValue] || 'Cash';

      if (source) {
        PaymentList = [
          {
            Type,
            Amount: TotalPrice?.TotalAmount?.Amount ?? source?.TotalPrice?.TotalAmount?.Amount,
            CurCode: TotalPrice?.TotalAmount?.CurCode ?? source?.TotalPrice?.TotalAmount?.CurCode,
            OrderItemID: orderItemIDs,
            OfferItemID: [],
            PaxRefID: [],
          },
        ];
      }

      if (infoCardCredit && checkedValue === 'credit_card') {
        PaymentList = [
          {
            ...PaymentList[0],
            PaymentCardMethod: {
              CardCode: infoCardCredit.CardCode,
              CardNumber: infoCardCredit.CardNumber,
              CardHolderName: infoCardCredit.CardHolderName,
              EffectiveExpireDate: {
                Expiration: infoCardCredit.Expiration,
              },
              SeriesCode: infoCardCredit.SeriesCode,
            },
          },
        ];
      }
    }

    dispatch('change', { PaymentList });
  }

  onMount(() => {
    const hasPayLater = airlineId !== 'TR' || (airlineId === 'TR' && typePayInformation === 'createPrice');
    if (hasPayLater && typePayInformation == 'createPrice' && hasPermission($user?.role, PERMISSIONS.PAY_LATER)) {
      rows = [
        ...rows,
        {
          id: 2,
          head: '나중결제',
          action: null,
        },
      ];
    }
  });
</script>

<DataTable {headers} {rows} class="g-custom-table paymentInformationTable" size="medium">
  <Toolbar size="sm">
    <ToolbarContent>
      <div class="table-toolbar">
        <div class="wrapper-title">
          <h4 class="title-content">결제 정보</h4>
        </div>
        <div class="wapper-actions-button" />
      </div>
    </ToolbarContent>
  </Toolbar>
  <div slot="cell" let:row let:cell class="cell-custom">
    {#if cell.key === 'action'}
      {#if row.id === 1}
        <div class="voucher-box" class:payment-disabled={!hasPermission($user?.role, PERMISSIONS.INSTANT_PAYMENT)}>
          {#if airlineId === 'TR'}
            <di class="voucher-cash">
              <RadioButton
                labelText="AGT"
                value="agt"
                checked={checkedValue === 'agt'}
                on:change={() => handleChangeCheckedValue('agt')}
              />
            </di>
          {:else if ['KE', 'HA', 'HAA'].includes(airlineId)}
            <di class="voucher-cash">
              <RadioButton
                labelText="Cash"
                value="cash"
                checked={checkedValue === 'cash'}
                on:change={() => handleChangeCheckedValue('cash')}
              />
            </di>
            <di class="voucher-cash">
              <RadioButton
                labelText="Credit Card"
                value="credit_card"
                checked={checkedValue === 'credit_card'}
                on:change={() => {
                  handleChangeCheckedValue('credit_card');
                  isOpenCreditCard = true;
                }}
              />
            </di>
          {:else}
            <di class="voucher-cash">
              <RadioButton
                labelText="Cash"
                value="cash"
                checked={checkedValue === 'cash'}
                on:change={() => handleChangeCheckedValue('cash')}
              />
            </di>
          {/if}

          {#if ['AF', 'KL'].includes(airlineId)}
            <div>
              <RadioButton
                labelText="Voucher"
                value="voucher"
                on:change={() => handleChangeCheckedValue('voucher')}
                checked={checkedValue === 'voucher'}
              />
              <div class="voucher-input">
                <CustomTextInput labelText="Voucher Number" placeholder="Voucher Number" />
              </div>
            </div>
            <div>
              <RadioButton
                labelText="혼합결제 (Voucher + Cash)"
                value="cash_voucher"
                checked={checkedValue === 'cash_voucher'}
                on:change={() => handleChangeCheckedValue('cash_voucher')}
              />
              <div class="voucher-input">
                <CustomTextInput labelText="Voucher Number" placeholder="Voucher Number" />
              </div>
            </div>
          {/if}
          {#if ['AY'].includes(airlineId) && typePayInformation == 'rePrice'}
            <div style="display: flex; justify-content: flex-end; width: 100%; height: 32px">
              <Button
                size="small"
                on:click={handleFirstOrderChange}
                disabled={statusCheckOrderChange === 'CALL_ORDER_CHANGE'}
              >
                {#if statusCheckOrderChange === 'CALLING_ORDER_CHANGE'}
                  <div class="g-custom-loading-button">
                    <InlineLoading description="Loading..." />
                  </div>
                {:else}
                  확인
                {/if}
              </Button>
            </div>
          {/if}
        </div>
      {:else if row.id === 2}
        <div class="voucher-box">
          <div>
            <RadioButton
              labelText="Pay Later"
              value="pay_later"
              checked={checkedValue === 'pay_later'}
              on:change={() => handleChangeCheckedValue('pay_later')}
            />
            <div class="notify">※ 결제가 되지 않으며 PNR만 생성됩니다</div>
          </div>
        </div>
      {/if}
    {:else}
      {cell.value}
    {/if}
  </div>
  <CreditCard
    open={isOpenCreditCard}
    on:close={() => {
      if (!infoCardCredit) {
        checkedValue = EMPTY;
      }
      isOpenCreditCard = false;
    }}
    on:submit-credit-card={(event) => (infoCardCredit = event.detail)}
  />
</DataTable>

<style>
  .payment-disabled {
    pointer-events: none;
    opacity: 0.3;
  }
</style>
