<script>
  import { createEventDispatcher } from 'svelte';
  import { Composed<PERSON>oda<PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from 'carbon-components-svelte';
  import { PrimaryButton } from 'src/components/common/button';
  import { ComboBox } from 'carbon-components-svelte';
  import Portal from 'src/components/common/Portal.svelte';
  import { CARD_CODES, EMPTY, InputOptions } from 'src/constants/app';
  import CustomTextInput from 'src/components/common/input/CustomTextInput.svelte';
  import 'src/styles/modal/credit-card.scss';
  export let open = false;
  const dispatch = createEventDispatcher();

  let cardCode = EMPTY;
  let cardNumber = EMPTY;
  let cardHolderName = EMPTY;
  let expiryMonth = EMPTY;
  let expiryYear = EMPTY;
  let cvcCode = EMPTY;
  let cardNumberSafe = EMPTY;
  let cvcCodeSafe = EMPTY;

  let validatedMessage = {
    cardNumber: EMPTY,
    expiryMonth: EMPTY,
    cvcCode: EMPTY,
  };

  function isAllowedKey(event) {
    let allowed = false;
    if (event.keyCode === 8 || event.keyCode === 9 || event.keyCode === 37 || event.keyCode === 39) {
      allowed = true;
    }
    return allowed;
  }

  function isTextSelected(input) {
    const startPosition = input.selectionStart;
    const endPosition = input.selectionEnd;

    const selObj = document.getSelection();
    const selectedText = selObj.toString();

    if (selectedText.length != 0) {
      input.focus();
      input.setSelectionRange(startPosition, endPosition);
      return true;
    } else if (input.value.substring(startPosition, endPosition).length != 0) {
      input.focus();
      input.setSelectionRange(startPosition, endPosition);
      return true;
    }
    return false;
  }

  function limit(event, element, max_chars) {
    if (isTextSelected(element)) {
      max_chars += 1;
    }
    if (element.value.length >= max_chars && !isAllowedKey(event)) {
      event.preventDefault();
    }
  }

  function formatCardNumber(event, element) {
    if (isNaN(event.key) && !isAllowedKey(event)) {
      event.preventDefault();
    } else {
      if (event.keyCode != 8) {
        if (element.value.length > 14) {
          const position = element.selectionStart;
          element.value = element.value.replace(/\W/gi, '').replace(/^(.{4})(.{4})(.{4})(.*)$/, '$1 $2 $3 $4');
          if (element.value.length != 19) {
            element.setSelectionRange(position, position);
          }
        } else {
          element.value = element.value.replace(/\W/gi, '').replace(/(.{4})/g, '$1 ');
        }
      }
    }
  }

  function handleCardNumberKeyDown(event) {
    const element = event.target;
    limit(event, element, 19);
    formatCardNumber(event, element);
  }

  function handleCardNumberInput(event) {
    const element = event.target;
    cardNumberSafe = element.value;
  }

  function handleCardNumberFocus() {
    cardNumber = cardNumberSafe;
  }

  function handleCardNumberBlur() {
    const parts = cardNumberSafe.split(' ');
    if (parts.length > 1) {
      for (let i = 0; i < parts.length; i++) {
        if (i == 1 || i == 2) {
          parts[i] = '*'.repeat(parts[i].length);
        }
      }
      cardNumber = parts.join(' ');
    }
  }

  function handleKeyPress(event) {
    const charCode = event.which || event.keyCode;
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
      return false;
    }
    return true;
  }

  function resetFormCreditCard() {
    cardCode = EMPTY;
    cardNumber = EMPTY;
    cardHolderName = EMPTY;
    expiryMonth = EMPTY;
    expiryYear = EMPTY;
    cvcCode = EMPTY;
    cardNumberSafe = EMPTY;
    cvcCodeSafe = EMPTY;
  }

  function validateForm() {
    validatedMessage = {
      cardNumber: EMPTY,
      expiryMonth: EMPTY,
      cvcCode: EMPTY,
    };
    if (cardNumberSafe.replace(/\s/g, '').length < 16) {
      validatedMessage.cardNumber = '정확한 카드번호를 입력해주세요.';
    }

    const month = parseInt(expiryMonth, 10);
    if (isNaN(month) || month < 1 || month > 12) {
      validatedMessage.expiryMonth = '정확한 유효연월을 입력해주세요.';
    }

    if (cvcCodeSafe.length < 3) {
      validatedMessage.cvcCode = '정확한 CVC 코드를 입력해주세요.';
    }
    if (validatedMessage.cardNumber || validatedMessage.expiryMonth || validatedMessage.cvcCode) return false;
    return true;
  }

  const handleSubmit = () => {
    if (!validateForm()) return;
    const formData = {
      CardCode: cardCode,
      CardNumber: cardNumberSafe.replace(/\s/g, ''),
      CardHolderName: cardHolderName,
      Expiration: expiryMonth + expiryYear,
      SeriesCode: cvcCodeSafe,
    };
    dispatch('submit-credit-card', formData);
    open = false;
  };
  $: isEnabledBtnSubmit = cardCode && cardNumber && cardHolderName && expiryMonth && expiryYear && cvcCode;
</script>

<Portal>
  <ComposedModal
    bind:open
    preventCloseOnClickOutside={true}
    size="sm"
    on:close={() => {
      resetFormCreditCard();
      dispatch('close');
    }}
  >
    <ModalHeader>
      <h4>카드 결제 정보 입력</h4>
    </ModalHeader>
    <ModalBody>
      <div class="credit-card-form">
        <div class="form-group">
          <div class="form-item form-item-with-number">
            <div class="form-field">
              <ComboBox titleText="카드사 선택" placeholder="Select" items={CARD_CODES} bind:selectedId={cardCode} />
            </div>
          </div>

          <div class="form-item form-item-with-number">
            <div class="form-field">
              <div class="bx--form-item bx--text-input-wrapper">
                <label for="cardNumber" class="bx--label">카드 번호</label>
                <div class="bx--text-input__field-outer-wrapper">
                  <div class="bx--text-input__field-wrapper">
                    <input
                      id="cardNumber"
                      placeholder="0000 **** **** 0000"
                      class="bx--text-input"
                      bind:value={cardNumber}
                      on:keypress={handleKeyPress}
                      on:keydown={handleCardNumberKeyDown}
                      on:input={handleCardNumberInput}
                      on:blur={handleCardNumberBlur}
                      on:focus={handleCardNumberFocus}
                    />
                  </div>
                  <div class="error-message">{validatedMessage.cardNumber}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="form-item form-item-with-number">
            <div class="form-field">
              <CustomTextInput
                labelText="카드 소유자 명"
                placeholder="HONG GILDONG"
                bind:value={cardHolderName}
                options={[InputOptions.UPPER_CASE, InputOptions.ONLY_LATIN]}
                ignoreRmSpace={true}
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-item form-item-with-number form-item-half">
              <div class="form-field">
                <div class="expiry-date-container">
                  <div class="expiry-date-label">유효기간 (Expiration Date)</div>
                  <div class="expiry-date-inputs">
                    <CustomTextInput
                      placeholder="MM"
                      bind:value={expiryMonth}
                      maxlength="2"
                      options={[InputOptions.ONLY_NUMBER]}
                    />
                    <span class="expiry-separator">/</span>
                    <CustomTextInput
                      placeholder="YY"
                      bind:value={expiryYear}
                      maxlength="2"
                      options={[InputOptions.ONLY_NUMBER]}
                    />
                  </div>
                  <div class="error-message">{validatedMessage.expiryMonth}</div>
                </div>
              </div>
            </div>

            <div class="form-item form-item-with-number form-item-half">
              <div class="form-field">
                <CustomTextInput
                  placeholder="789"
                  labelText="CVC 코드"
                  bind:value={cvcCode}
                  maxlength="3"
                  options={[InputOptions.ONLY_NUMBER]}
                  on:input={(event) => (cvcCodeSafe = event.detail)}
                  on:focus={() => (cvcCode = cvcCodeSafe)}
                  on:blur={() => (cvcCode = '*'.repeat(cvcCodeSafe.length))}
                />
                <div class="error-message">{validatedMessage.cvcCode}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ModalBody>
    <ModalFooter>
      <PrimaryButton on:click={handleSubmit} width="100%" disabled={!isEnabledBtnSubmit}>입력 완료</PrimaryButton>
    </ModalFooter>
  </ComposedModal>
</Portal>
