<script>
  import { createEventDispatcher } from 'svelte';
  import {
    ComposedModal,
    ModalHeader,
    ModalBody,
    ModalFooter,
    InlineLoading,
    DataTable,
    Checkbox,
  } from 'carbon-components-svelte';
  import { PrimaryButton } from 'src/components/common/button';
  import { EMPTY, APP_EVENT_NAME, CODE_SUCCESS } from 'src/constants/app.js';
  import { postOrderReshop, postOrderChange } from 'src/service/reservation';
  import CancellationRequest from 'src/components/modal/CancellationRequest.svelte';
  import { calcCancelTicketFee } from 'src/utils/appHelper';
  import { message } from 'src/store';
  import appEvent from 'src/store/appEventStore';
  import 'src/styles/modal/cancel-ticket-list.scss';

  const dispatch = createEventDispatcher();

  export let open = false;
  export let ticketList = [];

  let dataTable = [];
  let selectedRowIds = [];
  let openModalCancellationRequest = EMPTY;
  let isLoading = false;
  let isLoadingCancelTicket = false;
  let payloadCancelTicket = {};

  const headers = [
    {
      key: 'Action',
      value: '선택',
    },
    { key: 'TicketDocNbr', value: '티켓 번호' },
    { key: 'Type', value: '티켓 유형' },
    { key: 'PassengerName', value: '탑승객명(PTC)' },
    { key: 'TicketingDate', value: '발권 일시' },
    { key: 'segments', value: 'Segments' },
    { key: 'Edm', value: 'EMD 상세' },
    { key: 'TotalFareTicket', value: 'Total Fare' },
  ];

  function handleClickTicket(cell, row) {
    dispatch('handleClickCellOpenModal', { cell, row });
  }

  function closeModal() {
    open = false;
  }

  function handleSelectRowTable(e, row) {
    const status = e.detail;
    const id = row.id;
    if (status) {
      if (selectedRowIds.length <= dataTable.length - 1) {
        selectedRowIds = [...selectedRowIds, id];
      }
    } else {
      selectedRowIds = selectedRowIds.filter((selected) => selected !== id);
    }
  }

  async function submitCheckRefundAmount() {
    isLoading = true;
    const selectedTicketList = dataTable.filter((ticket) => selectedRowIds.includes(ticket.id));
    const deleteServiceList = selectedTicketList.map((ticket) => {
      const OrderItemRefID = ticket?.OrderItem?.OrderItemID || EMPTY;
      return {
        OrderItemRefID,
        RetainServiceID: [],
      };
    });
    const payload = {
      OrderID: selectedTicketList?.[0]?.OrderID || EMPTY,
      UpdateOrder: {
        ReshopOrder: {
          ServiceOrder: {
            Delete: deleteServiceList,
          },
        },
      },
    };
    try {
      const responseOrderReShop = await postOrderReshop(payload);
      if (responseOrderReShop.code === 200) {
        openModalCancellationRequest = calcCancelTicketFee(responseOrderReShop);
        const reshopOffer = responseOrderReShop?.data?.ReshopOffers?.[0];
        payloadCancelTicket = {
          query: {
            OrderID: selectedTicketList?.[0]?.OrderID || EMPTY,
            ChangeOrderChoice: {
              AcceptCancelledOffer: {
                OfferID: reshopOffer?.OfferID || EMPTY,
                Owner: reshopOffer?.Owner || EMPTY,
              },
            },
          },
        };
      }
    } catch (error) {
      console.error(error);
    } finally {
      isLoading = false;
    }
  }

  async function submitCancelTicket() {
    try {
      isLoadingCancelTicket = true;
      const { code, data } = await postOrderChange(payloadCancelTicket);
      if (code === 200 && data.ResultMessage.Code !== CODE_SUCCESS) {
        const errorMessage = data?.ResultMessage?.Message || '티켓 취소 처리 중 오류가 발생했습니다';
        message.set([
          {
            type: 'error',
            title: 'Error',
            subtitle: errorMessage,
          },
        ]);
        return;
      }

      // Success
      message.set([
        {
          type: 'success',
          title: 'Success',
          subtitle: '티켓이 정상적으로 취소 처리되었습니다',
        },
      ]);
      const selectedTicketList = dataTable.filter((ticket) => selectedRowIds.includes(ticket.id));
      const orderId = selectedTicketList?.[0]?.OrderID || EMPTY;
      appEvent.action(`${APP_EVENT_NAME.RELOAD_CURRENT_PNR_TICKET_LIST}${orderId}`, {
        detail: { resOrderChange: EMPTY, endMessage: EMPTY },
      });
    } catch (error) {
      console.error(error);
    } finally {
      isLoadingCancelTicket = false;
    }
  }

  $: {
    // Only EMD-A type
    const filteredTicketListByType = ticketList.filter((ticket) => {
      const ticketDoc = ticket?.TicketDoc || {};
      const isTypeIsEMD_A = ticketDoc?.Type === 'J';
      const isStatusOk = ticketDoc?.Status === 'OK';

      return isTypeIsEMD_A && isStatusOk;
    });

    dataTable = filteredTicketListByType;
  }
</script>

<ComposedModal bind:open size="lg" id="cancel-ticket-list-modal">
  <ModalHeader>
    <h4>취소할 티켓 선택</h4>
  </ModalHeader>
  <ModalBody>
    <div class="description">
      <div>구매 완료 한 티켓 중, 취소할 티켓(서비스)을 선택해 주세요.</div>
      <div>선택한 항목은 환불 요청이 완료된 후, 필요하면 다시 구매할 수 있습니다.</div>
    </div>

    <div>
      <DataTable bind:selectedRowIds {headers} rows={dataTable}>
        <div slot="cell" let:cell let:row>
          {#if ['Edm', 'segments'].includes(cell.key)}
            {#each cell.value as val}
              {#if val.length}
                <span>{val}</span><br />
              {/if}
            {/each}
          {:else if cell.key === 'TicketDocNbr'}
            <button on:click={() => handleClickTicket(cell, row)} class="g-button color-outstand-cell">
              {cell.value}
            </button>
          {:else if cell.key === 'Action'}
            <div>
              <Checkbox class="checkbox" on:check={(e) => handleSelectRowTable(e, row)} on:click={() => {}} />
            </div>
          {:else}
            {cell.value}
          {/if}
        </div>
      </DataTable>
    </div>
  </ModalBody>
  <ModalFooter>
    <footer>
      <PrimaryButton kind="secondary" width="100%" on:click={closeModal}>닫기</PrimaryButton>
      <PrimaryButton width="100%" disabled={selectedRowIds.length === 0} on:click={submitCheckRefundAmount}>
        {#if isLoading}
          <div class="g-custom-loading-button loading-box">
            <InlineLoading description="Loading ..." />
          </div>
        {/if} 환불 금액 확인
      </PrimaryButton>
    </footer>
  </ModalFooter>
</ComposedModal>

<CancellationRequest
  {openModalCancellationRequest}
  modalHeading="티켓 취소 확인"
  isLoading={isLoadingCancelTicket}
  on:primary-modal-cancellation-request={() => {
    submitCancelTicket();
  }}
  on:close-modal-cancellation-request={() => (openModalCancellationRequest = EMPTY)}
>
  <div slot="description">
    <p>선택한 티켓을 취소 및 환불합니다. 항공사 정책에 따라 환불-불가 금액이 발생할 수 있으며,</p>
    <p>환불-불가 금액은 차감 후 환불이 완료 됩니다.</p>
  </div>
</CancellationRequest>
