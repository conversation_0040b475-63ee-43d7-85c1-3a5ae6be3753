<script>
  import { createEventDispatcher } from 'svelte';
  import { message, screenIsNotEvent } from 'src/store';
  import {
    ComposedModal,
    ModalHeader,
    <PERSON>dalFooter,
    Button,
    ModalBody,
    RadioButton,
    InlineLoading,
    TextInput,
  } from 'carbon-components-svelte';
  import { cloneDeep } from 'lodash';
  import { PrimaryButton } from 'src/components/common/button';
  import { CustomTextInput } from 'src/components/common/input';
  import { isValidEmail } from 'src/utils/appHelper';
  import { EMPTY } from 'src/constants/app';
  import { userListService } from 'src/service';
  import 'src/styles/modal/add-new-user.scss';

  const dispatch = createEventDispatcher();

  export let userAdd = false;

  const USER_ROLES = {
    TICKETING_USER: 'TICKETING_USER',
    USER: 'USER',
  };

  const FORM_OPTIONS_INIT = {
    role: USER_ROLES.TICKETING_USER,
    username: EMPTY,
    countryDialingCode: EMPTY,
    phoneNumber: EMPTY,
    etc: EMPTY,
  };

  const INVALID_OPTIONS_INIT = {
    username: {
      invalid: false,
      invalidText: EMPTY,
    },
    countryDialingCode: {
      invalid: false,
      invalidText: EMPTY,
    },
  };

  const userTypeDescriptions = {
    [USER_ROLES.TICKETING_USER]: '유저 관리를 제외한 LUNA의 모든 예약/발권 기능을 사용 할 수 있습니다.',
    [USER_ROLES.USER]: 'LUNA의 유저 관리 및 발권 기능은 사용할 수 없으며, 예약 조회 기능에만 접근 가능합니다.',
  };

  let formOptions = cloneDeep(FORM_OPTIONS_INIT);
  let isLoading = false;
  let isErrorApi = EMPTY;
  let invalidOptions = cloneDeep(INVALID_OPTIONS_INIT);

  let disabledSubmit = false;

  function handleErrorValid(key, value) {
    const validators = {
      username: [
        { test: (v) => !v?.trim(), msg: 'ⓘ 이메일을 입력해 주세요' },
        { test: (v) => isValidEmail(v), msg: 'ⓘ 올바른 이메일을 입력해 주세요' },
      ],
      countryDialingCode: [{ test: (v) => !v?.trim(), msg: '① 미입력 : 모바일 번호를 입력해 주세요' }],
    };

    const rules = validators[key] || [];
    const rule = rules.find((r) => r.test(value));

    if (key === 'phoneNumber') {
      // Same as countryDialingCode
      handleErrorValid('countryDialingCode', value);
    }

    invalidOptions = {
      ...invalidOptions,
      [key]: {
        invalid: Boolean(rule),
        invalidText: rule?.msg || EMPTY,
      },
    };
  }

  function onChangeInput(e, key) {
    let value = e?.detail || EMPTY;
    switch (key) {
      case 'phoneNumber':
      case 'countryDialingCode':
        value = value?.replace(/\D/g, EMPTY);
        break;
      case 'emailAddress':
        value = value.replace(/[ㄱ-ㅎㅏ-ㅣ가-힣\s]/g, EMPTY);
        break;
      default:
        break;
    }

    handleErrorValid(key, value);
    formOptions[key] = value;
    isErrorApi = EMPTY;
  }

  function onCloseModal() {
    userAdd = false;
    resetForm();
  }

  function resetForm() {
    formOptions = cloneDeep(FORM_OPTIONS_INIT);
    invalidOptions = cloneDeep(INVALID_OPTIONS_INIT);
  }

  async function onSubmit() {
    const forms = {
      username: formOptions.username,
      countryDialingCode: formOptions.countryDialingCode,
      phoneNumber: formOptions.phoneNumber,
    };

    // Validate again
    for (const key in forms) {
      handleErrorValid(key, forms[key]);
    }
    const hasValid = Object.values(invalidOptions).every((option) => !option.invalid);
    if (!hasValid) return;

    const payload = formOptions;

    isLoading = true;
    screenIsNotEvent.set(true);
    const response = await userListService.addNewUser(payload);

    const { code, data } = response || {};

    if (code === 200) {
      message.update(() => [
        {
          type: 'success',
          title: 'Success',
          subtitle: '유저가 생성되었습니다',
          caption: EMPTY,
        },
      ]);
      onCloseModal();
      dispatch('success');
    } else {
      isErrorApi = true;
    }

    screenIsNotEvent.set(false);
    isLoading = false;
  }

  $: {
    const hasValid = Object.values(invalidOptions).every((option) => !option.invalid);
    disabledSubmit =
      isErrorApi || !formOptions.username || !formOptions.phoneNumber || !formOptions.countryDialingCode || !hasValid;
  }
</script>

<ComposedModal
  bind:open={userAdd}
  preventCloseOnClickOutside={true}
  on:close={onCloseModal}
  size="lg"
  id="add-new-user-modal"
>
  <ModalHeader title="신규 유저 등록" />
  <ModalBody class="content-add-new-user">
    <p>
      새로운 Luna User를 생성합니다. 이 화면에서는 Luna Admin 권한을 가진 사용자만 하위 계정을 추가할 수 있습니다. <br
      />
      생성된 계정은 등록 시 입력한 이메일로 초기 로그인 안내가 발송되며, 지정된 유저 타입에 따라 접근 가능한 기능이 자동
      제한됩니다.
    </p>
    <br />
    <table class="user-info-table">
      <thead>
        <tr>
          <th style="width:20%;">유저 타입 선택</th>
          <th style="width:80%;" />
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>
            <RadioButton
              labelText="Ticketing User"
              checked={formOptions.role === USER_ROLES.TICKETING_USER}
              on:change={() => (formOptions.role = USER_ROLES.TICKETING_USER)}
            />
          </td>
          <td>{userTypeDescriptions[USER_ROLES.TICKETING_USER]}</td>
        </tr>
        <tr>
          <td>
            <RadioButton
              labelText="User"
              checked={formOptions.role === USER_ROLES.USER}
              on:change={() => (formOptions.role = USER_ROLES.USER)}
            />
          </td>
          <td>{userTypeDescriptions[USER_ROLES.USER]}</td>
        </tr>
      </tbody>
    </table>
    <table class="user-info-table">
      <thead>
        <tr>
          <th style="width:20%;">Email (ID)</th>
          <th style="width:80%;">Mobile Number</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>
            <CustomTextInput
              bind:value={formOptions.username}
              invalid={invalidOptions.username.invalid}
              invalidText={invalidOptions.username.invalidText}
              on:input={(e) => onChangeInput(e, 'username')}
            />
          </td>
          <td>
            <div class="mobile-number-container">
              <CustomTextInput
                class="mobile-number-dial-code"
                maxLength={3}
                bind:value={formOptions.countryDialingCode}
                invalid={invalidOptions.countryDialingCode.invalid}
                invalidText={invalidOptions.countryDialingCode.invalidText}
                on:input={(e) => onChangeInput(e, 'countryDialingCode')}
              />
              <CustomTextInput
                class="mobile-number-number"
                bind:value={formOptions.phoneNumber}
                on:input={(e) => onChangeInput(e, 'phoneNumber')}
              />
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <div class="user-profile-table">
      <div class="user-profile-header">기타 정보 입력</div>
      <div class="user-profile-row">
        <TextInput
          maxLength={100}
          bind:value={formOptions.etc}
          on:input={(e) => onChangeInput(e, 'etc')}
          placeholder="기록해두고 싶은 참고사항이 있다면 입력해 주세요. (예 : 유저 이름, 담당 항공사, 업무 시간, 연락 유의 사항 등)"
        />
      </div>
    </div>
  </ModalBody>
  <ModalFooter>
    <footer>
      <PrimaryButton kind="primary" class="btn-add-user" disabled={disabledSubmit} on:click={onSubmit}>
        신규 유저 생성{#if isLoading}
          <div class="g-custom-loading-button loading-box">
            <InlineLoading description="Loading ..." />
          </div>
        {/if}</PrimaryButton
      >
    </footer>
  </ModalFooter>
</ComposedModal>

<style>
</style>
