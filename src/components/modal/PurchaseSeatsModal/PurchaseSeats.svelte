<script>
  import { onMount } from 'svelte';
  import { isEmpty, intersection, uniqWith, isEqual, cloneDeep } from 'lodash';

  import {
    Tabs,
    Tab,
    TabContent,
    ComposedModal,
    ModalBody,
    ModalHeader,
    ModalFooter,
    InlineLoading,
  } from 'carbon-components-svelte';

  import ChangePaymentPnr from 'src/components/modal/ChangePaymentPnr.svelte';
  import SelectSeatContent from 'src/components/modal/PurchaseSeatsModal/SelectSeatContent.svelte';
  import { PrimaryButton } from 'src/components/common/button';
  import Portal from 'src/components/common/Portal.svelte';

  import { EMPTY, CODE_SUCCESS, APP_EVENT_NAME } from 'src/constants/app';
  import { message, screenIsNotEvent } from 'src/store';
  import { postOrderChange, postOrderQuote } from 'src/service/reservation';
  import { getOfferPrice } from 'src/service/reservationTicketing';
  import { getAmountAndOrderItemIDList, getSsrData, groupData } from 'src/utils/appHelper';
  import { PERMISSIONS, hasPermission } from 'src/utils/rolePermissions.js';
  import { user } from 'src/store';

  import 'src/styles/modal/purchase-seats.scss';
  import appEvent from 'src/store/appEventStore';

  export let open;
  export let orderRetrieve;
  export let seatData;
  export let orderId;

  const airlineId = seatData.AlaCarteOffer.Owner;
  let paxList = [];
  let tabs = [];
  let tabsContent = {};
  let priceAmount = {
    Amount: 0,
    CurCode: EMPTY,
  };
  let selectedPtc = EMPTY;
  let ptcSelectedSeat = {};
  let ptcHasSeat = [];
  let ptcCacheHasSeat = [];
  let statusLoadingCalculatePrice = 'NONE';
  let changePaymentPnrData = {
    open: false,
    payload: {},
  };
  let offerPriceOrOrderQuoteData = EMPTY;
  let loadingSeatSelection = false;

  function handleCloseModal() {
    open = false;
    seatData = EMPTY;
  }

  const getHeaderTabs = (PaxSegmentList) => {
    return PaxSegmentList.map((paxSegment) => ({
      id: paxSegment.PaxSegmentID,
      label: `${paxSegment.PaxSegmentID}: ${paxSegment.Departure.AirportCode}-${paxSegment.Arrival.AirportCode}`,
      PaxSegmentID: paxSegment.PaxSegmentID,
    }));
  };

  const handleOpenPaymentPnrModal = async () => {
    loadingSeatSelection = true;
    let OfferItems = [];
    if (['AF', 'KL'].includes(airlineId)) {
      OfferItems = offerPriceOrOrderQuoteData.PricedOffer.OfferItem.map((item) => {
        return {
          OfferItemID: item.OfferItemID,
          PaxRefID: [item.Service[0].PaxRefID],
          SeatSelection: {
            Column: item.Service[0]?.SelectedSeat?.Column ?? EMPTY,
            Row: item.Service[0]?.SelectedSeat?.Row ?? EMPTY,
          },
        };
      });
    } else if (['SQ', 'AY', 'TR'].includes(airlineId)) {
      OfferItems = offerPriceOrOrderQuoteData?.ReshopOffers[0]?.AddOfferItem.map((item) => {
        return {
          OfferItemID: item.OfferItemID,
          PaxRefID: item.PaxRefID,
          SeatSelection: {
            Column: item.Service[0]?.SelectedSeat?.Column ?? EMPTY,
            Row: item.Service[0]?.SelectedSeat?.Row ?? EMPTY,
          },
        };
      });
    } else {
      OfferItems = ptcHasSeat.map((ptc) => {
        return {
          OfferItemID: intersection(ptc.offerItemIDWhiteList, ptc.seat.OfferItemRefID)[0],
          PaxRefID: [ptc.paxId],
          SeatSelection: {
            Column: ptc.seat.column,
            Row: ptc.seat.row,
          },
        };
      });
    }

    let payload = {
      query: {
        OrderID: orderId,
        ChangeOrderChoice: {
          AcceptSelectedQuotedOfferList: {
            SelectedPricedOffer: [
              {
                OfferID: ['AF', 'KL'].includes(airlineId)
                  ? offerPriceOrOrderQuoteData.PricedOffer.OfferID
                  : ['SQ', 'AY', 'TR'].includes(airlineId)
                  ? offerPriceOrOrderQuoteData.ReshopOffers[0]?.OfferID
                  : seatData.AlaCarteOffer.OfferID,
                Owner: airlineId,
                ResponseID: ['AF', 'KL'].includes(airlineId)
                  ? offerPriceOrOrderQuoteData.PricedOffer.ResponseID
                  : ['SQ', 'AY', 'TR'].includes(airlineId)
                  ? offerPriceOrOrderQuoteData.ReshopOffers[0]?.ResponseID
                  : seatData.ResponseID,
                OfferItems,
              },
            ],
          },
        },
      },
    };

    // TODO: LUNAVN-280
    // SeatAvailability - OrderChange - OrderRetrieve. (LH, LX, OS, EK, QR)
    if (['QR', 'AA'].includes(airlineId)) {
      if (priceAmount.Amount == 0) {
        const response = await postOrderChange({
          ...payload,
          query: {
            ...payload.query,
            PaymentList: [
              {
                Type: 'Cash',
                Amount: 0,
                CurCode: priceAmount.CurCode,
                OfferItemID: OfferItems.map((item) => item.OfferItemID),
                OrderItemID: [],
                PaxRefID: [],
              },
            ],
          },
        });
        appEvent.action(`${APP_EVENT_NAME.RELOAD_CURRENT_PNR_CHANGEPAYMENTPNRMODAL}${orderId}`, {
          detail: { resOrderChange: EMPTY, endMessage: EMPTY },
        });
      }
      loadingSeatSelection = false;
    }

    // SeatAvailability - OfferPrice - OrderChange - OrderRetrieve. (AF, KL)
    if (['AF', 'KL'].includes(airlineId)) {
      const res = await postOrderChange({
        ...payload,
        query: {
          ...payload.query,
          PaymentList: [],
        },
      });
      loadingSeatSelection = false;
      if (res?.data?.ResultMessage?.Code !== CODE_SUCCESS) {
        message.update(() => [
          {
            type: 'error',
            title: 'Error',
            subtitle: `${res?.data?.ResultMessage?.Code} ${res?.data?.ResultMessage?.Message}`,
            caption: EMPTY,
          },
        ]);
        return;
      }
      if (priceAmount.Amount == 0 && ['AF', 'KL'].includes(airlineId)) {
        appEvent.action(`${APP_EVENT_NAME.RELOAD_CURRENT_PNR_CHANGEPAYMENTPNRMODAL}${orderId}`, {
          detail: { resOrderChange: EMPTY, endMessage: EMPTY },
        });
        return;
      } else {
        const ssrTable = groupData(getSsrData(res, airlineId));
        const { PaymentList } = res?.data?.DataLists ?? [];
        const { OrderItemIDList, Amount = 0, CurCode = EMPTY } = getAmountAndOrderItemIDList(ssrTable, PaymentList);
        const payloadAFKL = {
          OrderID: orderId,
          ChangeOrderChoice: {
            AcceptRepricedOrder: {
              OfferRefID: [orderId],
            },
            UpdatePax: [],
          },
          PaxList: [],
          ContactInfoList: [],
          PaymentList: [
            {
              Type: 'Cash',
              Amount,
              CurCode,
              PaxRefID: [],
              OrderItemID: OrderItemIDList,
              OfferItemID: [],
            },
          ],
        };
        changePaymentPnrData = {
          open: true,
          payload: {
            query: payloadAFKL,
          },
        };
        return;
      }
    }

    // SeatAvailability - OrderChange - OrderRetrieve. (SQ, AY)
    if (['SQ', 'AY', 'TR'].includes(airlineId)) {
      if (priceAmount.Amount == 0) {
        const res = await postOrderChange({
          ...payload,
          query: {
            ...payload.query,
            PaymentList: [],
          },
        });
        if (res?.data?.ResultMessage?.Code !== CODE_SUCCESS) {
          message.update(() => [
            {
              type: 'error',
              title: 'Error',
              subtitle: `${res?.data?.ResultMessage?.Code} ${res?.data?.ResultMessage?.Message}`,
              caption: EMPTY,
            },
          ]);
          loadingSeatSelection = false;
          return;
        }
        appEvent.action(`${APP_EVENT_NAME.RELOAD_CURRENT_PNR_CHANGEPAYMENTPNRMODAL}${orderId}`, {
          detail: { resOrderChange: EMPTY, endMessage: EMPTY },
        });
      }
      loadingSeatSelection = false;
    }

    if (['LH', 'LHA', 'LX', 'LXA', 'OS', 'EK', 'KE', 'HA', 'HAA'].includes(airlineId)) {
      const response = await postOrderChange({
        ...payload,
        query: {
          ...payload.query,
          PaymentList: [
            {
              Type: 'Cash',
              Amount: 0,
              CurCode: priceAmount.CurCode,
              OfferItemID: OfferItems.map((item) => item.OfferItemID),
              OrderItemID: [],
              PaxRefID: [],
            },
          ],
        },
      });
      if (response?.data?.ResultMessage?.Code !== CODE_SUCCESS) {
        message.update(() => [
          {
            type: 'error',
            title: 'Error',
            subtitle: `${response?.data?.ResultMessage?.Code} ${response?.data?.ResultMessage?.Message}`,
            caption: EMPTY,
          },
        ]);
        loadingSeatSelection = false;
        return;
      }
      loadingSeatSelection = false;
      appEvent.action(`${APP_EVENT_NAME.RELOAD_CURRENT_PNR_CHANGEPAYMENTPNRMODAL}${orderId}`, {
        detail: { resOrderChange: EMPTY, endMessage: EMPTY },
      });
    }
    // Code done task : LUNAVN-280

    if (!['AF', 'KL'].includes(airlineId)) {
      payload['query']['PaymentList'] = [
        {
          Type: 'Cash',
          Amount: priceAmount.Amount,
          CurCode: priceAmount.CurCode,
          OfferItemID: OfferItems.map((item) => item.OfferItemID),
          OrderItemID: [],
          PaxRefID: [],
        },
      ];
    }

    changePaymentPnrData = {
      open: true,
      payload,
    };
  };

  const handleCalculatePrice = async (event) => {
    const type = event.detail;
    const OfferItems = ptcHasSeat.map((ptc) => {
      return {
        OfferItemID: intersection(ptc.offerItemIDWhiteList, ptc.seat.OfferItemRefID)[0],
        PaxRefID: [ptc.paxId],
        SeatSelection: {
          Column: ptc.seat.column,
          Row: ptc.seat.row,
        },
      };
    });
    let PaxList = ptcHasSeat.map((ptc) => ({
      PaxID: ptc.paxId,
      Ptc: ptc.type,
      LoyaltyProgramAccount: [],
    }));
    PaxList = uniqWith(PaxList, isEqual);
    const Criteria = {
      ExistingOrderCriteria: {
        OrderID: orderRetrieve?.data?.Order?.OrderID,
        PaxRefID: PaxList.map((pax) => pax.PaxID),
      },
    };
    const Query = {
      Offers: [
        {
          OfferID: seatData.AlaCarteOffer.OfferID,
          Owner: airlineId,
          ResponseID: seatData.ResponseID,
          OfferItems,
        },
      ],
      PaxList,
      Criteria,
    };
    const queryOrderQuote = {
      OrderID: orderRetrieve?.data?.Order?.OrderID,
      SelectedOffer: [
        {
          OfferID: seatData.AlaCarteOffer.OfferID,
          Owner: airlineId,
          OfferItems,
        },
      ],
    };
    try {
      statusLoadingCalculatePrice = 'OFFER_PRICE_ORDER_QUOTE';
      const { code, data } =
        type === 'offerQuote' ? await postOrderQuote({ query: queryOrderQuote }) : await getOfferPrice({ Query });
      if (code === 200 && data?.ResultMessage?.Code === CODE_SUCCESS) {
        ptcCacheHasSeat = cloneDeep(ptcHasSeat);
        if (type === 'offerQuote') {
          priceAmount = data.ReshopOffers[0]?.TotalPrice?.TotalAmount ?? EMPTY;
        } else if (type === 'offerPrice') {
          priceAmount = data.PricedOffer.TotalPrice.TotalAmount;
        }
        offerPriceOrOrderQuoteData = data;
      }
    } catch (error) {
      console.log(error);
    }
    statusLoadingCalculatePrice = 'ORDER_CHANGE';
  };

  onMount(() => {
    paxList = orderRetrieve?.data?.DataLists?.PaxList ?? [];
    tabs = getHeaderTabs(orderRetrieve?.data?.DataLists?.PaxSegmentList ?? []);
  });

  $: if (!isEmpty(ptcSelectedSeat)) {
    ptcHasSeat = [];
    for (const key in ptcSelectedSeat) {
      ptcHasSeat = [...ptcHasSeat, ...ptcSelectedSeat[key].filter((ptc) => ptc.seat !== EMPTY)];
    }
    if (!['AF', 'KL'].includes(airlineId)) {
      priceAmount = ptcHasSeat.reduce(
        (acc, currentPtc) => {
          acc.Amount += currentPtc?.totalAmount?.Amount ?? 0;
          if (!!currentPtc.totalAmount.CurCode) acc.CurCode = currentPtc.totalAmount.CurCode;
          return acc;
        },
        {
          Amount: 0,
          CurCode: EMPTY,
        }
      );
    }
  }

  $: screenIsNotEvent.set(['OFFER_PRICE_ORDER_QUOTE'].includes(statusLoadingCalculatePrice));

  $: {
    const isNotShowCalculatePriceBtn = !['AF', 'KL', 'SQ', 'AY', 'TR'].includes(airlineId);
    const isHasAtleastOneSeatSelected = Object.entries(ptcSelectedSeat).some(([key, ptcItem]) =>
      ptcItem.some((ptcItem) => ptcItem.seat !== EMPTY)
    );

    if (isNotShowCalculatePriceBtn) {
      statusLoadingCalculatePrice = isHasAtleastOneSeatSelected ? 'ORDER_CHANGE' : 'NONE';
    }
  }

  $: userRole = $user?.role;
</script>

<Portal>
  <ChangePaymentPnr
    {orderId}
    bind:open={changePaymentPnrData.open}
    airline={airlineId}
    payload={changePaymentPnrData.payload}
    totalAmount={priceAmount}
  />
  <div id="purchase-seats" style="--tabs-cols:{tabs.length > 4 ? 'auto-fit' : 4}">
    <ComposedModal bind:open size="lg" preventCloseOnClickOutside={true} on:close={handleCloseModal}>
      <ModalHeader />
      <ModalBody>
        <Tabs type="container">
          {#each tabs as tab}
            <Tab label={tab.label} />
          {/each}
          <div slot="content">
            {#each tabs as { PaxSegmentID }}
              <TabContent>
                <svelte:component
                  this={SelectSeatContent}
                  bind:this={tabsContent[PaxSegmentID]}
                  {paxList}
                  {PaxSegmentID}
                  airlineID={airlineId}
                  SeatMap={seatData?.SeatMap ?? []}
                  AlaCarteOfferItem={seatData?.AlaCarteOffer?.AlaCarteOfferItem ?? []}
                  bind:priceAmount
                  on:changeSelectedPtc={(event) => {
                    selectedPtc = event.detail;
                  }}
                  on:change={(event) => {
                    ptcSelectedSeat[PaxSegmentID] = event.detail;
                  }}
                  on:calculate-price={handleCalculatePrice}
                  disabledCalculatePrice={isEqual(ptcCacheHasSeat, ptcHasSeat)}
                  loadingCalculatePrice={statusLoadingCalculatePrice}
                />
              </TabContent>
            {/each}
          </div>
        </Tabs>
      </ModalBody>
      <ModalFooter>
        <footer>
          <PrimaryButton kind="secondary" width="100%" on:click={handleCloseModal}>취소</PrimaryButton>
          <PrimaryButton
            width="100%"
            disabled={!(statusLoadingCalculatePrice == 'ORDER_CHANGE') ||
              !hasPermission(userRole, PERMISSIONS.PURCHASE_SEAT_SERVICE)}
            on:click={handleOpenPaymentPnrModal}
          >
            {#if loadingSeatSelection}
              <div class="g-custom-loading-button">
                <InlineLoading description="Loading" />
              </div>
            {:else}
              좌석선택
            {/if}
          </PrimaryButton>
        </footer>
      </ModalFooter>
    </ComposedModal>
  </div>
</Portal>
