<script>
  import {
    ComposedModal,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ooter,
    ComboBox,
    InlineLoading,
    Checkbox,
    RadioButton,
    RadioButtonGroup,
  } from 'carbon-components-svelte';
  import { AddFilled } from 'carbon-icons-svelte';
  import { createEventDispatcher } from 'svelte';
  import { PrimaryButton } from 'src/components/common/button';
  import { EMPTY, CODE_SUCCESS } from 'src/constants/app';
  import { isEmpty, uniq, find, compact, cloneDeep } from 'lodash';
  import { mappingByKeysValue, sleep } from 'src/utils/appHelper';
  import { loadingBtn, screenIsNotEvent } from 'src/store';
  import { postOrderChange } from 'src/service/reservation';
  import InfoChangeInfoTooltip from 'src/components/modal/change-passenger-information/InfoChangeInfoTooltip.svelte';
  import { hasPermission, PERMISSIONS } from 'src/utils/rolePermissions.js';
  import { user } from 'src/store';
  import 'src/styles/modal/change-passenger-information.scss';

  export let open = false;
  export let orderId = EMPTY;
  export let airlineID = EMPTY;
  export let passengerInfo = {};

  const GROUP_AIRLINES_1 = ['AF', 'KL'];
  const GROUP_AIRLINES_2 = ['SQ', 'AY'];
  const GROUP_AIRLINES_3 = ['KE', 'HA', 'HAA'];

  const METHOD = {
    UPDATE: 'UPDATE',
    ADD_DELETE: 'ADD_DELETE',
  };

  const TYPE = {
    PAX_INFO: 'Pax Info',
    CONTACT_INFO: 'Contact Info',
  };

  const DEFAULT_SETTING = {
    paxInfo: {
      isEditable: true,
      isAddOrDelete: false,
    },
    contactInfo: {
      isEditable: true,
      isAddOrDelete: false,
    },
  };

  const SETTING_MAP = {
    'AF, KL': DEFAULT_SETTING,
    'SQ, AY': {
      paxInfo: {
        isEditable: false,
        isAddOrDelete: false,
      },
      contactInfo: {
        isEditable: false,
        isAddOrDelete: true,
      },
    },
    'KE, HA, HAA': {
      paxInfo: {
        isEditable: true,
        isAddOrDelete: false,
      },
      contactInfo: {
        isEditable: true,
        isAddOrDelete: true,
      },
    },
  };

  let dropdownChangeList = [
    { id: 'Contact Info', value: TYPE.CONTACT_INFO, key: 'contactInfo' },
    { id: 'Pax Info', value: TYPE.PAX_INFO, key: 'paxInfo' },
  ];

  let radioButtonMethodList = [
    { id: 1, value: METHOD.UPDATE, label: '변경' },
    { id: 2, value: METHOD.ADD_DELETE, label: '추가/삭제' },
  ];

  const dispatch = createEventDispatcher();
  const isGroupAirline3 = GROUP_AIRLINES_3.includes(airlineID);

  let setting = DEFAULT_SETTING;
  let hasEdit = false;
  let passengerInformations = [];
  let selectedDropdown = undefined;
  let addPassengerInformation = false;
  let deletePassengerInformation = false;

  let isClickAddContactInfo = EMPTY;
  let isShowError = false;
  let confirmedInputs = [];
  let selectedCheckboxes = [];
  let activeParentId = null;
  let resultCheckLimitCheckbox = EMPTY;
  let parentAddContactInfo = EMPTY;
  let selectedMethod = EMPTY;
  let messageError = EMPTY;
  let focusedPassengerId = EMPTY;

  function onChangePassengerInformation(rawValue, row, key) {
    let value = rawValue || EMPTY;
    switch (key) {
      case 'surName':
      case 'givenName':
      case 'nameTitle':
        value = value?.replace(/[^a-zA-Z\s]/g, EMPTY);
        break;
      case 'rawPhoneNumber':
      case 'countryDialCode':
        value = value?.replace(/\D/g, EMPTY);
        break;
      case 'emailAddress':
        value = value.replace(/[ㄱ-ㅎㅏ-ㅣ가-힣\s]/g, EMPTY);
        break;
      default:
        break;
    }
    passengerInformations = passengerInformations.map((el) => {
      if (el.id === row.id) {
        el[key] = value;
        const initialValue = passengerInfo?.dataTable.find((el) => el.id === row.id)?.[key] ?? EMPTY;
        if (value == initialValue) {
          el.editList = el.editList.filter((item) => item !== key);
        } else {
          el.editList = uniq([...el.editList, key]);
        }
      }
      return el;
    });
  }

  function handleCloseModal() {
    open = false;
  }

  function getPaxListPayloadByAirline(paxList, updatedPaxRefId) {
    const updatedPaxList = [];
    paxList.forEach((paxItem) => {
      const foundPassengerItem = passengerInformations.find((passengerItem) => passengerItem.paxID === paxItem.PaxID);
      const { nameTitle = EMPTY, surName = EMPTY, givenName = EMPTY } = foundPassengerItem;
      const { NameTitle = EMPTY, Surname = EMPTY, GivenName = EMPTY } = paxItem.Individual;
      const isChangeNameTitle = nameTitle !== NameTitle;
      const isChangeSurName = surName !== Surname;
      const isChangeGivenName = givenName !== GivenName[0];
      if (isChangeNameTitle || isChangeSurName || isChangeGivenName) {
        updatedPaxRefId.push(paxItem.PaxID);
        let updatedPaxItem = {
          ...paxItem,
          Individual: {
            ...paxItem.Individual,
            NameTitle: nameTitle,
            Surname: surName,
            GivenName: [givenName],
          },
        };

        if (isGroupAirline3) {
          // KE, HA (HAA)
          const originalPaxItem = {
            PaxID: paxItem.PaxID,
            Ptc: paxItem.Ptc,
            ContactInfoRefID: [],
            Individual: {
              ...(isChangeGivenName && { GivenName: paxItem.Individual.GivenName }),
              ...(isChangeSurName && { Surname: paxItem.Individual.Surname }),
              MiddleName: [],
            },
            IdentityDoc: [],
            LoyaltyProgramAccount: [],
          };
          updatedPaxList.push(originalPaxItem);
          updatedPaxItem = cloneDeep(originalPaxItem);

          const MAPPING_KEYS = [
            { key: 'Individual.GivenName[0]', value: givenName },
            { key: 'Individual.Surname', value: surName },
          ];

          updatedPaxItem = mappingByKeysValue(MAPPING_KEYS, updatedPaxItem);
          updatedPaxItem.PaxID = updatedPaxItem.PaxID.concat('-1');
        }
        updatedPaxList.push(updatedPaxItem);
      }
    });

    return updatedPaxList;
  }

  function getContactInfoWithCondition(isChangePhoneNumber, isChangeEmail, isChangeCountryDialCode, foundContactItem) {
    const rawContactItem = cloneDeep(foundContactItem);
    const isSendDialCode = isChangeCountryDialCode && rawContactItem.Phone?.[0]?.CountryDialingCode;

    return {
      ContactInfoID: rawContactItem?.ContactInfoID,
      ...((isChangePhoneNumber || isSendDialCode) && {
        Phone: [
          {
            ...(isChangePhoneNumber && { PhoneNumber: rawContactItem.Phone?.[0]?.PhoneNumber }),
            ...(isSendDialCode && {
              CountryDialCode: rawContactItem.Phone[0].CountryDialingCode,
            }),
          },
        ],
      }),
      ...(isChangeEmail && { EmailAddress: [rawContactItem?.EmailAddress?.[0]] }),
    };
  }

  function getContactInfoPayloadByAirline(airlineID, paxList, contactInfoList, updatedContactRefId) {
    const updatedContactInfoList = [];
    for (const paxItem of paxList) {
      const isCheckGetContactInfo =
        airlineID === 'LJ' && ['ADT', 'CHD'].includes(Ptc) && isEmpty(paxItem?.ContactInfoRefID);
      const foundContactItem = isCheckGetContactInfo
        ? contactInfoList[0]
        : find(contactInfoList, (elm) => paxItem?.ContactInfoRefID.includes(elm?.ContactInfoID));

      const foundPassengerItem = passengerInformations.find((passengerItem) => passengerItem.paxID === paxItem.PaxID);
      const { rawPhoneNumber = EMPTY, emailAddress = EMPTY, countryDialCode = EMPTY } = foundPassengerItem;
      if (!foundContactItem) {
        if (paxItem.ContactInfoRefID.length > 0) {
          updatedContactRefId.push(paxItem.PaxID);
          const updatedContactInfo = {
            ContactInfoID: paxItem.ContactInfoRefID[0],
            EmailAddress: [emailAddress],
            Phone: [{ PhoneNumber: rawPhoneNumber, ...(countryDialCode && { CountryDialCode: countryDialCode }) }],
            PostalAddress: [],
          };
          updatedContactInfoList.push(updatedContactInfo);
        }
        continue;
      }
      const { Phone, EmailAddress } = foundContactItem;
      const isChangePhoneNumber = rawPhoneNumber !== (Phone?.[0]?.PhoneNumber || EMPTY);
      const isChangeEmail = emailAddress !== (EmailAddress?.[0] || EMPTY);
      const isChangeCountryDialCode = countryDialCode !== (Phone?.[0]?.CountryDialingCode || EMPTY);

      if (isChangePhoneNumber || isChangeEmail || isChangeCountryDialCode) {
        updatedContactRefId.push(paxItem.PaxID);
        let updatedContactInfo = {};

        if (isGroupAirline3) {
          // KE, HA (HAA)
          const contactItem = getContactInfoWithCondition(
            isChangePhoneNumber,
            isChangeEmail,
            isChangeCountryDialCode,
            foundContactItem
          );

          updatedContactInfoList.push(contactItem);
          updatedContactInfo = cloneDeep(contactItem);
          updatedContactInfo.ContactInfoID = contactItem.ContactInfoID.concat('-1');

          const MAPPING_KEYS = [
            { key: 'Phone[0].PhoneNumber', value: rawPhoneNumber },
            { key: 'Phone[0].CountryDialCode', value: countryDialCode },
            { key: 'EmailAddress[0]', value: emailAddress },
          ];

          updatedContactInfo = mappingByKeysValue(MAPPING_KEYS, updatedContactInfo);
        } else {
          updatedContactInfo = {
            ...foundContactItem,
            Phone: [{ PhoneNumber: rawPhoneNumber, ...(countryDialCode && { CountryDialCode: countryDialCode }) }],
            EmailAddress: [emailAddress],
          };
        }
        updatedContactInfoList.push(updatedContactInfo);
      }
    }

    return updatedContactInfoList;
  }

  async function handleUpdatePassengerInformation() {
    const { contactInfoList, paxList } = passengerInfo.dataTable[0];
    const selectedPaxInfo = selectedDropdown === 'Pax Info';
    const updatedPaxRefId = [];
    const updatedPaxList = getPaxListPayloadByAirline(paxList, updatedPaxRefId);
    const updatedContactRefId = [];
    const updatedContactInfoList = getContactInfoPayloadByAirline(
      airlineID,
      paxList,
      contactInfoList,
      updatedContactRefId
    );

    const queryBase = {
      OrderID: orderId,
      PaymentList: [],
      ChangeOrderChoice: {},
      ContactInfoList: [],
      PaxList: [],
    };

    if (selectedPaxInfo) {
      queryBase.PaxList = compact(updatedPaxList);
      queryBase.ChangeOrderChoice.UpdatePax = updatedPaxRefId.map((el) => ({
        CurrentPaxRefID: el,
        ...(isGroupAirline3 && { NewPaxRefID: el.concat('-1') }),
      }));
    } else {
      queryBase.ContactInfoList = compact(updatedContactInfoList);
      if (isGroupAirline3) {
        // KE, HA (HAA)
        const paxListChanged = [];
        for (const paxRefId of updatedContactRefId) {
          const foundPaxItem = paxList.find((paxItem) => paxItem.PaxID === paxRefId);
          if (foundPaxItem) {
            const { Individual, ...basePaxItem } = foundPaxItem;
            const updatedPaxItem = {
              ...basePaxItem,
              PaxID: basePaxItem.PaxID.concat('-1'),
              ContactInfoRefID: basePaxItem.ContactInfoRefID.map((el) => el.concat('-1')),
            };
            paxListChanged.push(...[basePaxItem, updatedPaxItem]);
          }
        }
        queryBase.PaxList = paxListChanged;
      }
      queryBase.ChangeOrderChoice.UpdatePax = updatedContactRefId.map((el) => ({
        CurrentPaxRefID: el,
        ...(isGroupAirline3 && { NewPaxRefID: el.concat('-1') }),
      }));
    }

    try {
      screenIsNotEvent.set(true);
      const { code, data } = await postOrderChange({
        query: queryBase,
      });
      if (code === 200 && data.ResultMessage?.Code === CODE_SUCCESS) {
        await sleep(2000);
        dispatch('reload-current-pnr', { resOrderChange: data, endMessage: EMPTY });
        screenIsNotEvent.set(false);
      }
    } catch (error) {
      console.log('error', error);
    } finally {
      screenIsNotEvent.set(false);
    }
  }

  function selectChangedItem(e) {
    const { selectedId = EMPTY } = e?.detail;
    const disabledList =
      selectedId === 'Contact Info' ? ['nameTitle', 'surName', 'givenName'] : ['rawPhoneNumber', 'emailAddress'];
    setInitialPassengerInformations(disabledList);
  }

  function setInitialPassengerInformations(disabledList = []) {
    focusedPassengerId = EMPTY;
    passengerInformations = passengerInfo?.dataTable.map((el) => ({
      ...el,
      disabledList,
      hasEdit: false,
      editList: [],
    }));
  }

  function setupSetting(airlineID) {
    const getKeyList = (key) => key.split(',').map((el) => el.trim());

    for (const [key, value] of Object.entries(SETTING_MAP)) {
      if (getKeyList(key).includes(airlineID)) {
        setting = value;
      }
    }

    dropdownChangeList = dropdownChangeList.map((el) => {
      el.disabled = !(setting[el.key].isEditable || setting[el.key].isAddOrDelete);
      return el;
    });

    const canUpdateInfo = setting.paxInfo.isEditable || setting.contactInfo.isEditable;
    const canAddOrDeleteInfo = setting.paxInfo.isAddOrDelete || setting.contactInfo.isAddOrDelete;

    radioButtonMethodList = radioButtonMethodList.map((el, index) => {
      el.disabled = index === 0 ? !canUpdateInfo : !canAddOrDeleteInfo;
      return el;
    });

    if (GROUP_AIRLINES_2.includes(airlineID)) {
      selectedDropdown = TYPE.CONTACT_INFO;
      selectedMethod = METHOD.ADD_DELETE;
    } else {
      selectedDropdown = TYPE.PAX_INFO;
      selectedMethod = METHOD.UPDATE;
    }
  }

  function isRowEmpty(child) {
    return !child.rawPhoneNumber && !child.emailAddress;
  }

  function handleCheckboxChange(event, item, child, field) {
    const checkboxId = child ? `${item.id}-${item.contactInfoChildren.indexOf(child)}-${field}` : `${item.id}-${field}`;

    if (event.target.checked) {
      if (!activeParentId) activeParentId = item.id;
      selectedCheckboxes = [...selectedCheckboxes, checkboxId];
    } else {
      selectedCheckboxes = selectedCheckboxes.filter((id) => id !== checkboxId);
    }
    if (!selectedCheckboxes.length) activeParentId = null;
    resultCheckLimitCheckbox = checkLimitCheckbox(item);
    deletePassengerInformation = selectedCheckboxes.length > 0;
  }

  function countCheckboxInItem(item) {
    let totalCheckboxInParent = 0;
    if (item.rawPhoneNumber) totalCheckboxInParent += 1;
    if (item.emailAddress) totalCheckboxInParent += 1;
    for (const element of item.contactInfoChildren) {
      if (element.rawPhoneNumber) totalCheckboxInParent += 1;
      if (element.emailAddress) totalCheckboxInParent += 1;
    }
    return totalCheckboxInParent;
  }

  function checkLimitCheckbox(item) {
    let totalRowInParent = countCheckboxInItem(item);
    let totalRowChecked = 0;
    let keyDisableCheckbox = [];
    const itemId = item.id;
    if (selectedCheckboxes.includes(`${itemId}-phone`)) {
      totalRowChecked += 1;
    } else {
      keyDisableCheckbox = [...keyDisableCheckbox, `${itemId}-phone`];
    }
    if (selectedCheckboxes.includes(`${itemId}-email`)) {
      totalRowChecked += 1;
    } else {
      keyDisableCheckbox = [...keyDisableCheckbox, `${itemId}-email`];
    }

    for (let i = 0; i < item.contactInfoChildren.length; i++) {
      if (selectedCheckboxes.includes(`${itemId}-${i}-phone`)) {
        totalRowChecked += 1;
      } else {
        keyDisableCheckbox = [...keyDisableCheckbox, `${itemId}-${i}-phone`];
      }
      if (selectedCheckboxes.includes(`${itemId}-${i}-email`)) {
        totalRowChecked += 1;
      } else {
        keyDisableCheckbox = [...keyDisableCheckbox, `${itemId}-${i}-email`];
      }
    }
    return {
      isLimitCheckbox: totalRowInParent - totalRowChecked === 1,
      keyDisableCheckbox: keyDisableCheckbox,
    };
  }

  function handleInput(child, field, value) {
    if (['rawPhoneNumber', 'countryDialCode'].includes(field)) {
      const numbersOnly = value.replace(/\D/g, EMPTY);
      child[field] = numbersOnly;
    } else {
      child[field] = value;
    }
    passengerInformations = [...passengerInformations];
  }

  function addContactInfo(item) {
    isClickAddContactInfo = item.id;

    passengerInformations = passengerInformations.map((elm) => {
      if (elm.id === isClickAddContactInfo && elm.contactInfoChildren.length < 4) {
        return {
          ...elm,
          contactInfoChildren: [
            ...elm.contactInfoChildren,
            {
              rawPhoneNumber: EMPTY,
              emailAddress: EMPTY,
              status: 'new',
            },
          ],
        };
      }
      return elm;
    });

    addPassengerInformation = true;
  }

  function handleBlur(event, item, child, field) {
    const value = event.target.value.trim();
    const childIndex = item.contactInfoChildren.indexOf(child);
    const inputId = `${item.id}-${childIndex}-${field}`;

    if (value) {
      if (!confirmedInputs.includes(inputId)) {
        confirmedInputs = [...confirmedInputs, inputId];
      }
    } else {
      confirmedInputs = confirmedInputs.filter((id) => id !== inputId);
    }
  }

  function handleKeyDown(event) {
    if (event.key === 'Enter') {
      event.target.blur();
    }
  }

  async function handleAddPassengerInformation() {
    const hasEmptyRow = passengerInformations.some(
      (item) => item.contactInfoChildren && item.contactInfoChildren.some((child) => isRowEmpty(child))
    );
    if (hasEmptyRow) {
      isShowError = true;
      return;
    }
    const dataAddContactInfo = {
      EmailAddress: [],
      Phone: [],
    };
    for (const elm of passengerInformations) {
      for (const item of elm.contactInfoChildren) {
        if (item.status === 'new') {
          parentAddContactInfo = elm;
          if (item.rawPhoneNumber) {
            dataAddContactInfo.Phone.push({
              PhoneNumber: item.rawPhoneNumber,
              ...(item.countryDialCode && { CountryDialCode: item.countryDialCode }),
            });
          }
          if (item.emailAddress) {
            dataAddContactInfo.EmailAddress.push(item.emailAddress);
          }
        }
      }
    }

    const isDeleteOnlyEmail = dataAddContactInfo.Phone.length === 0;
    const contactInfoRefIDs = parentAddContactInfo?.contactInfoRefID.filter((elm) => !elm.includes('OTH'));
    const updatedPaxList = parentAddContactInfo?.paxList.filter((paxItem) => {
      return paxItem.PaxID === parentAddContactInfo.paxID;
    });
    const queryBase = {
      OrderID: orderId,
      PaymentList: [],
      ChangeOrderChoice: {
        UpdatePax: [
          {
            NewPaxRefID: parentAddContactInfo.paxID,
          },
        ],
      },
      ContactInfoList: [
        {
          ContactInfoID:
            contactInfoRefIDs.length === 1
              ? contactInfoRefIDs[0]
              : isDeleteOnlyEmail
              ? contactInfoRefIDs[1]
              : contactInfoRefIDs[0],
          EmailAddress: dataAddContactInfo.EmailAddress,
          Phone: dataAddContactInfo.Phone,
          PostalAddress: [],
        },
      ],
      PaxList: updatedPaxList,
    };
    try {
      loadingBtn.set('ADDITIONAL_PASSENGER_INFORMATION');
      const res = await postOrderChange({
        query: queryBase,
      });
      if (res.code === 200 && res.data.ResultMessage?.Code === CODE_SUCCESS) {
        await sleep(2000);
        dispatch('reload-current-pnr', { resOrderChange: res.data, endMessage: EMPTY });
        loadingBtn.set(EMPTY);
      }
    } catch (error) {
      console.log('error', error);
    } finally {
      loadingBtn.set(EMPTY);
    }
    return !hasEmptyRow;
  }

  function getPayloadDeleteContactInfo(passengerInformations) {
    const queryBase = {
      OrderID: orderId,
      PaymentList: [],
      ChangeOrderChoice: {},
      ContactInfoList: [],
      PaxList: [],
    };

    const selectedIdPaxItem = Number(selectedCheckboxes[0].split('-')[0]);
    const selectedPaxItem = passengerInformations.find((passenger) => passenger.id === selectedIdPaxItem);
    const contactInfoRefIDs = selectedPaxItem?.contactInfoRefID.filter((elm) => !elm.includes('OTH'));
    if (selectedPaxItem && selectedPaxItem?.contactInfoRefID?.length > 0) {
      const rawContactValues = selectedPaxItem.contactInfoChildren.filter((contact) => contact.status === 'old');
      const resultsMapping = { EmailAddress: [], Phone: [] };
      const clonedSelectedCheckboxes = cloneDeep(selectedCheckboxes);
      clonedSelectedCheckboxes
        .sort((x, y) => x.length - y.length || x.localeCompare(y))
        .forEach((box) => {
          const [, position, key] = box.split('-');
          const isRoot = isNaN(position);
          const valueChoosen = isRoot ? selectedPaxItem : rawContactValues[Number(position)];
          const positionChoosen = isRoot ? position : key;
          if (positionChoosen === 'email') resultsMapping.EmailAddress.push(valueChoosen.emailAddress);
          else
            resultsMapping.Phone.push(
              !valueChoosen?.countryDialCode
                ? { PhoneNumber: valueChoosen.rawPhoneNumber }
                : { PhoneNumber: valueChoosen.rawPhoneNumber, CountryDialingCode: valueChoosen.countryDialCode }
            );
        });

      const isDeleteOnlyEmail = resultsMapping.Phone.length === 0;
      queryBase.ChangeOrderChoice.UpdatePax = [{ CurrentPaxRefID: selectedPaxItem.paxID }];
      queryBase.ContactInfoList = [
        {
          ContactInfoID:
            contactInfoRefIDs.length === 1
              ? contactInfoRefIDs[0]
              : isDeleteOnlyEmail
              ? contactInfoRefIDs[1]
              : contactInfoRefIDs[0],
          EmailAddress: resultsMapping.EmailAddress,
          Phone: resultsMapping.Phone,
          PostalAddress: [],
        },
      ];

      const updatedPaxList = selectedPaxItem?.paxList.filter((paxItem) => {
        return paxItem.PaxID === selectedPaxItem.paxID;
      });
      queryBase.PaxList = updatedPaxList;
    }

    return queryBase;
  }

  async function handleDeletePassengerInformation() {
    const query = getPayloadDeleteContactInfo(passengerInformations);
    try {
      loadingBtn.set('DELETE_PASSENGER_INFORMATION');
      const res = await postOrderChange({
        query,
      });
      if (res?.code === 200 && res?.data.ResultMessage?.Code === CODE_SUCCESS) {
        await sleep(2000);
        dispatch('reload-current-pnr', { resOrderChange: res?.data, endMessage: EMPTY });
        loadingBtn.set(EMPTY);
      }
    } catch (error) {
      console.log('error', error);
    } finally {
      loadingBtn.set(EMPTY);
    }
  }

  async function handleChangePassengerInformation() {
    if (selectedMethod === METHOD.UPDATE) {
      handleUpdatePassengerInformation();
    }

    if (selectedMethod === METHOD.ADD_DELETE) {
      if (addPassengerInformation) {
        handleAddPassengerInformation();
      } else {
        handleDeletePassengerInformation();
      }
    }
  }

  function handleChangeMethod(e) {
    const method = e.detail;
    if (method === METHOD.UPDATE) {
      // Reset add or delete action
      selectedCheckboxes = [];
      confirmedInputs = [];
      activeParentId = null;
      resultCheckLimitCheckbox = EMPTY;
      addPassengerInformation = false;
      deletePassengerInformation = false;
      isClickAddContactInfo = EMPTY;
      isShowError = false;
      passengerInformations = passengerInformations.map((item) => {
        item.contactInfoChildren = item.contactInfoChildren.filter((child) => child.status !== 'new');
        return item;
      });
    }

    if (method === METHOD.ADD_DELETE) {
      // Reset update information
      setInitialPassengerInformations();
    }
  }

  function checkDisplayInput(value) {
    if (!value || value?.ptc === 'INF' || (value && value.trim() === '')) return false;
    return true;
  }

  function onFocusInputEditInformation(row) {
    if (selectedMethod === METHOD.ADD_DELETE) return;
    focusedPassengerId = row.id;
  }

  function onBlurInputEditInformation(row) {
    if (selectedMethod === METHOD.ADD_DELETE) return;
    const editList = row.editList;
    if (editList.length === 0) focusedPassengerId = EMPTY;
  }

  $: {
    if (!isEmpty(passengerInfo?.dataTable)) {
      setInitialPassengerInformations();
      setupSetting(airlineID);
    }
  }

  $: {
    hasEdit =
      passengerInformations.find((passenger) => !isEmpty(passenger?.editList)) &&
      !(selectedDropdown === EMPTY || selectedDropdown === undefined);
  }
  $: {
    if (
      GROUP_AIRLINES_3.includes(airlineID) &&
      selectedDropdown === TYPE.PAX_INFO &&
      selectedMethod === METHOD.ADD_DELETE
    )
      messageError = 'ⓘ 탑승객 개인 정보는 변경만 가능합니다.';
    else messageError = EMPTY;
  }

  $: userRole = $user.role;
</script>

<div id="changePassengerInformation">
  <ComposedModal {open} size="lg" preventCloseOnClickOutside={true} on:close={handleCloseModal}>
    <ModalHeader>
      <h4>탑승객 정보 수정</h4>
    </ModalHeader>
    <ModalBody>
      <div class="description">
        <div>탑승객 정보를 수정합니다.</div>
        <div>
          유아 탑승객의 휴대전화번호 및 이메일은 지정된 보호자의 정보와 동일하게 사용되며, 개별 수정은 불가능합니다.
        </div>
        <div class="passenger-information-container">
          {#if messageError}
            <p class="text-danger">{messageError}</p>
          {/if}
          <div class="passenger-information-box">
            <div class="passenger-information-dropdown">
              <p>변경항목 선택</p>
              <ComboBox
                placeholder="Select"
                bind:selectedId={selectedDropdown}
                items={dropdownChangeList}
                on:select={selectChangedItem}
                on:clear={() => {
                  setInitialPassengerInformations();
                }}
              />
            </div>
            <div class="passenger-information-method">
              <div class="passenger-information-method-tooltip">
                <p>정보 수정 방식</p>
                <div>
                  <InfoChangeInfoTooltip />
                </div>
              </div>
              <RadioButtonGroup hideLegend bind:selected={selectedMethod} on:change={handleChangeMethod}>
                {#each radioButtonMethodList as radioButton (radioButton.id)}
                  <RadioButton
                    labelText={radioButton.label}
                    value={radioButton.value}
                    disabled={radioButton.disabled}
                  />
                {/each}
              </RadioButtonGroup>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="bx--data-table-container">
          <table class="bx--data-table bx--data-table--short" style="table-layout: fixed;">
            <thead>
              <tr>
                <th style="width: 5%;"><div class="bx--table-header-label">추가</div></th>
                <th style="width: 5%;"><div class="bx--table-header-label">순번</div></th>
                <th style="width: 5%;"><div class="bx--table-header-label">PTC</div></th>
                <th style="width: 8%;"><div class="bx--table-header-label">Title</div></th>
                <th style="width: 12%;"><div class="bx--table-header-label">SurName</div></th>
                <th style="width: 12%;"><div class="bx--table-header-label">GivenName</div></th>
                <th style="width: 20%;"
                  ><div class="bx--table-header-label">
                    <input type="checkbox" style="visibility: hidden" />
                    <span>Mobile</span>
                  </div></th
                >
                <th style="width: 20%;"
                  ><div class="bx--table-header-label">
                    <input type="checkbox" style="visibility: hidden" />
                    <span> Email </span>
                  </div></th
                >
              </tr>
            </thead>
            <tbody aria-live="polite">
              {#each passengerInformations as item, index (item)}
                {@const isSelectContactInfo = selectedDropdown !== TYPE.PAX_INFO}
                {@const isFocused = focusedPassengerId === EMPTY ? true : focusedPassengerId === item.id}
                {@const isDisableUpdatePaxInfo =
                  isSelectContactInfo || messageError || item.ptc === 'INF' || !isFocused}
                {@const isSelectUpdateInfoMethod = selectedMethod === METHOD.UPDATE}
                <tr>
                  <td>
                    {#if (!isClickAddContactInfo || item.id === isClickAddContactInfo) && selectedCheckboxes.length === 0 && item.contactInfoChildren.length !== 4 && item.ptc !== 'INF'}
                      <p
                        class="add"
                        class:disabled={isSelectUpdateInfoMethod || messageError}
                        on:click={() => {
                          if (isSelectUpdateInfoMethod) return;
                          addContactInfo(item);
                        }}
                      >
                        <AddFilled />
                      </p>
                    {/if}
                  </td>

                  <td><div>{item.id}</div></td>
                  <td><div>{item.ptc}</div></td>
                  <td>
                    <div>
                      <input
                        class={`bx--text-input ${item.editList.includes('nameTitle') ? 'edited' : EMPTY}`}
                        disabled={isDisableUpdatePaxInfo}
                        bind:value={item.nameTitle}
                        on:input={(e) => {
                          onChangePassengerInformation(e.target.value, { id: index + 1 }, 'nameTitle');
                        }}
                        on:focus={onFocusInputEditInformation(item)}
                        on:blur={onBlurInputEditInformation(item)}
                      />
                    </div>
                  </td>
                  <td>
                    <div>
                      <input
                        class={`bx--text-input ${item.editList.includes('surName') ? 'edited' : EMPTY}`}
                        disabled={isDisableUpdatePaxInfo}
                        bind:value={item.surName}
                        on:input={(e) => {
                          onChangePassengerInformation(e.target.value, { id: index + 1 }, 'surName');
                        }}
                        on:focus={onFocusInputEditInformation(item)}
                        on:blur={onBlurInputEditInformation(item)}
                      />
                    </div>
                  </td>
                  <td>
                    <div>
                      <input
                        class={`bx--text-input ${item.editList.includes('givenName') ? 'edited' : EMPTY}`}
                        disabled={isDisableUpdatePaxInfo}
                        bind:value={item.givenName}
                        on:input={(e) => {
                          onChangePassengerInformation(e.target.value, { id: index + 1 }, 'givenName');
                        }}
                        on:focus={onFocusInputEditInformation(item)}
                        on:blur={onBlurInputEditInformation(item)}
                      />
                    </div>
                  </td>
                  <td>
                    {#if checkDisplayInput(item?.phoneNumber)}
                      {@const classInput = `bx--text-input ${
                        selectedCheckboxes.includes(`${item.id}-phone`) ? 'selected-for-delete' : EMPTY
                      }`}
                      <div class="checkbox-input-container">
                        {#if !isClickAddContactInfo}
                          <Checkbox
                            on:change={(e) => handleCheckboxChange(e, item, null, 'phone')}
                            checked={selectedCheckboxes.includes(`${item.id}-phone`)}
                            disabled={!isSelectContactInfo ||
                              isSelectUpdateInfoMethod ||
                              (activeParentId && activeParentId !== item.id) ||
                              (resultCheckLimitCheckbox?.keyDisableCheckbox?.includes(`${item.id}-phone`) &&
                                resultCheckLimitCheckbox?.isLimitCheckbox) ||
                              countCheckboxInItem(item) === 1 ||
                              !isFocused}
                          />
                        {/if}
                        <input
                          class={`${classInput + ' input-country-dial-code '} ${
                            item.editList.includes('countryDialCode') ? 'edited' : EMPTY
                          }`}
                          bind:value={item.countryDialCode}
                          disabled={!isSelectContactInfo || !isFocused}
                          readonly={selectedMethod === METHOD.ADD_DELETE}
                          maxlength="3"
                          on:input={(e) => {
                            onChangePassengerInformation(e.target.value, { id: index + 1 }, 'countryDialCode');
                          }}
                          on:focus={onFocusInputEditInformation(item)}
                          on:blur={onBlurInputEditInformation(item)}
                        />
                        <input
                          class={classInput + `${item.editList.includes('rawPhoneNumber') ? 'edited' : EMPTY}`}
                          bind:value={item.rawPhoneNumber}
                          disabled={!isSelectContactInfo || !isFocused}
                          readonly={selectedMethod === METHOD.ADD_DELETE}
                          placeholder="숫자를 입력해주세요"
                          on:input={(e) => {
                            onChangePassengerInformation(e.target.value, { id: index + 1 }, 'rawPhoneNumber');
                          }}
                          on:focus={onFocusInputEditInformation(item)}
                          on:blur={onBlurInputEditInformation(item)}
                        />
                      </div>
                    {/if}
                  </td>
                  <td>
                    {#if checkDisplayInput(item?.baseEmailAddress)}
                      <div class="checkbox-input-container">
                        {#if !isClickAddContactInfo}
                          <Checkbox
                            on:change={(e) => handleCheckboxChange(e, item, null, 'email')}
                            checked={selectedCheckboxes.includes(`${item.id}-email`)}
                            disabled={!isSelectContactInfo ||
                              isSelectUpdateInfoMethod ||
                              (activeParentId && activeParentId !== item.id) ||
                              (resultCheckLimitCheckbox?.keyDisableCheckbox?.includes(`${item.id}-email`) &&
                                resultCheckLimitCheckbox?.isLimitCheckbox) ||
                              countCheckboxInItem(item) === 1 ||
                              !isFocused}
                          />
                        {/if}
                        <input
                          class="bx--text-input {selectedCheckboxes.includes(`${item.id}-email`)
                            ? 'selected-for-delete'
                            : EMPTY} {item.editList.includes('emailAddress') ? 'edited' : EMPTY}"
                          bind:value={item.emailAddress}
                          disabled={!isSelectContactInfo || !isFocused}
                          readonly={selectedMethod === METHOD.ADD_DELETE}
                          on:input={(e) => {
                            onChangePassengerInformation(e.target.value, { id: index + 1 }, 'emailAddress');
                          }}
                          on:focus={onFocusInputEditInformation(item)}
                          on:blur={onBlurInputEditInformation(item)}
                        />
                      </div>
                    {/if}
                  </td>
                </tr>

                {#if item.contactInfoChildren.length}
                  {#each item.contactInfoChildren as child, childIndex (child)}
                    {@const isDisableContactInfoChildren =
                      selectedDropdown === TYPE.PAX_INFO || selectedMethod === METHOD.UPDATE}
                    <tr>
                      <td />
                      <td />
                      <td />
                      <td />
                      <td />
                      <td />
                      <td>
                        {#if !(child.status === 'old' && !child.rawPhoneNumber)}
                          {@const classInput = `bx--text-input 
                              ${
                                confirmedInputs.includes(`${item.id}-${childIndex}-phone`) && child.status === 'new'
                                  ? 'confirmed'
                                  : EMPTY
                              } 
                              ${
                                selectedCheckboxes.includes(`${item.id}-${childIndex}-phone`)
                                  ? 'selected-for-delete'
                                  : EMPTY
                              }`}
                          <div class="checkbox-input-container">
                            {#if !isClickAddContactInfo}
                              <Checkbox
                                on:change={(e) => handleCheckboxChange(e, item, child, 'phone')}
                                checked={selectedCheckboxes.includes(`${item.id}-${childIndex}-phone`)}
                                disabled={isDisableContactInfoChildren ||
                                  (activeParentId && activeParentId !== item.id) ||
                                  (resultCheckLimitCheckbox?.keyDisableCheckbox?.includes(
                                    `${item.id}-${childIndex}-phone`
                                  ) &&
                                    resultCheckLimitCheckbox?.isLimitCheckbox)}
                              />
                            {/if}
                            <input
                              class={classInput + ' input-country-dial-code'}
                              bind:value={child.countryDialCode}
                              disabled={isDisableContactInfoChildren}
                              readonly={child.status === 'old'}
                              on:input={(e) => handleInput(child, 'countryDialCode', e.target.value)}
                              on:blur={(e) => handleBlur(e, item, child, 'phone')}
                              on:keydown={handleKeyDown}
                            />
                            <input
                              class={classInput}
                              bind:value={child.rawPhoneNumber}
                              disabled={isDisableContactInfoChildren}
                              readonly={child.status === 'old'}
                              on:input={(e) => handleInput(child, 'rawPhoneNumber', e.target.value)}
                              on:blur={(e) => handleBlur(e, item, child, 'phone')}
                              on:keydown={handleKeyDown}
                              placeholder="숫자를 입력해주세요"
                            />
                          </div>
                          {#if isShowError && isRowEmpty(child) && child.status === 'new'}
                            <p
                              class="error-text-input"
                              style={`padding-left: ${!isClickAddContactInfo ? '28px' : '0px'}`}
                            >
                              탑승객의 정보를 입력해 주세요.
                            </p>
                          {/if}
                        {/if}
                      </td>
                      <td>
                        {#if !(child.status === 'old' && !child.emailAddress)}
                          <div class="checkbox-input-container">
                            {#if !isClickAddContactInfo}
                              <Checkbox
                                on:change={(e) => handleCheckboxChange(e, item, child, 'email')}
                                checked={selectedCheckboxes.includes(`${item.id}-${childIndex}-email`)}
                                disabled={isDisableContactInfoChildren ||
                                  (activeParentId && activeParentId !== item.id) ||
                                  (resultCheckLimitCheckbox?.keyDisableCheckbox?.includes(
                                    `${item.id}-${childIndex}-email`
                                  ) &&
                                    resultCheckLimitCheckbox?.isLimitCheckbox)}
                              />
                            {/if}
                            <input
                              class="bx--text-input
                              {confirmedInputs.includes(`${item.id}-${childIndex}-email`) && child.status === 'new'
                                ? 'confirmed'
                                : EMPTY} 
                              {selectedCheckboxes.includes(`${item.id}-${childIndex}-email`)
                                ? 'selected-for-delete'
                                : EMPTY}"
                              value={child.emailAddress}
                              disabled={isDisableContactInfoChildren}
                              readonly={child.status === 'old'}
                              on:input={(e) => handleInput(child, 'emailAddress', e.target.value)}
                              on:blur={(e) => handleBlur(e, item, child, 'email')}
                              on:keydown={handleKeyDown}
                            />
                          </div>
                          {#if isShowError && isRowEmpty(child) && child.status === 'new'}
                            <p
                              class="error-text-input"
                              style={`padding-left: ${!isClickAddContactInfo ? '28px' : '0px'}`}
                            >
                              탑승객의 정보를 입력해 주세요.
                            </p>
                          {/if}
                        {/if}
                      </td>
                    </tr>
                  {/each}
                {/if}
              {/each}
            </tbody>
          </table>
        </div>
      </div>
    </ModalBody>
    <ModalFooter>
      <footer>
        <PrimaryButton kind="secondary" width="100%" on:click={handleCloseModal}>취소</PrimaryButton>
        <PrimaryButton
          width="100%"
          disabled={(!hasEdit && !addPassengerInformation && !deletePassengerInformation) ||
            !hasPermission(userRole, PERMISSIONS.CHANGE_USER_INFORMATION)}
          on:click={handleChangePassengerInformation}
          >탑승객 정보 변경
          {#if $screenIsNotEvent || ['ADDITIONAL_PASSENGER_INFORMATION', 'DELETE_PASSENGER_INFORMATION'].includes($loadingBtn)}
            <div class="g-custom-loading-button">
              <InlineLoading description="Loading ..." />
            </div>
          {/if}
        </PrimaryButton>
      </footer>
    </ModalFooter>
  </ComposedModal>
</div>
