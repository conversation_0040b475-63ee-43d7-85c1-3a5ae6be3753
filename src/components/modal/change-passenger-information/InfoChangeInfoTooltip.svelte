<script>
  import { CustomTooltip } from 'src/components/common/tooltip';

  export let align = 'center';
  export let direction = 'top';
</script>

<CustomTooltip {align} {direction}>
  <div class="info-change-info-tooltip" slot="content">
    <p><strong>[변경]</strong> 기존 정보를 새로운 정보로 수정합니다.</p>
    <p><strong>[추가/삭제]</strong> 탑승객 1명의 정보를 추가하거나 삭제할 수 있습니다.</p>
    <p>- ⊕ 버튼: 클릭 시 추가 정보 입력란이 열립니다.</p>
    <p>- 체크박스: 삭제할 정보를 선택합니다.</p>
  </div>
</CustomTooltip>

<style>
  .info-change-info-tooltip {
    padding: 8px;
  }

  .info-change-info-tooltip p {
    font-size: 14px;
  }
</style>
