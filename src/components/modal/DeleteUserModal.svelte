<script>
  import { createEventDispatcher } from 'svelte';
  import { ComposedModal, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ModalBody } from 'carbon-components-svelte';
  import { PrimaryButton } from 'src/components/common/button';
  import ConfirmModal from 'src/components/modal/ConfirmModal.svelte';
  import { EMPTY } from 'src/constants/app';
  import { userListService } from 'src/service';
  import { message } from 'src/store';
  import { hasPermission, PERMISSIONS } from 'src/utils/rolePermissions';
  import { user } from 'src/store';
  import { USER_TYPE_DESCRIPTION } from 'src/constants/userList.js';

  const dispatch = createEventDispatcher();

  export let userDelete = EMPTY;
  let dialog;

  $: userRole = $user?.role;
  $: isAdmin = userDelete.userType === 'Admin';
</script>

<ComposedModal
  bind:open={userDelete}
  preventCloseOnClickOutside={true}
  on:close={() => (userDelete = EMPTY)}
  size="lg"
  class="delete-user-modal"
>
  <ModalHeader title="유저 정보 확인" />
  <ModalBody class="content-delete-user">
    <p>
      선택한 유저의 상세 정보입니다. 계정 생성 후에는 유저 타입과 개인정보를 수정할 수 없습니다. <br />
      타입을 바꾸려면 해당 유저를 삭제한 뒤 새로 생성해야 합니다.
    </p>
    <br />
    <table class="user-info-table">
      <thead>
        <tr>
          <th style="width:20%;">유저 타입</th>
          <th style="width:80%;" />
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>
            <span class="user-type-label"><b>{USER_TYPE_DESCRIPTION[userDelete.userType]?.name || EMPTY}</b></span>
          </td>
          <td>{USER_TYPE_DESCRIPTION[userDelete.userType]?.desc || EMPTY}</td>
        </tr>
      </tbody>
    </table>
    <table class="user-info-table">
      <thead>
        <tr>
          <th style="width:20%;">Email (ID)</th>
          <th style="width:80%;">Mobile Number</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>{userDelete.email}</td>
          <td>{userDelete.phoneNumber}</td>
        </tr>
      </tbody>
    </table>
    <div class="user-profile-table">
      <div class="user-profile-header">기타 정보</div>
      <div class="user-profile-row">{userDelete.profile}</div>
    </div>
    <ConfirmModal
      bind:dialog
      on:confirm-delete-user={async () => {
        try {
          const response = await userListService.deleteUser(userDelete.username);
          message.update(() => [
            {
              type: 'success',
              title: 'Success',
              subtitle: response.message,
              caption: EMPTY,
            },
          ]);
          dispatch('delete-success');
        } catch (error) {}
      }}
    />
  </ModalBody>
  <ModalFooter>
    <div class="footer-actions">
      <PrimaryButton
        disabled={!hasPermission(userRole, PERMISSIONS.DELETE_USER) || isAdmin}
        kind="danger"
        class="footer-btn"
        on:click={() => {
          dialog.showModal();
        }}>유저 삭제</PrimaryButton
      >
      <PrimaryButton
        kind="secondary"
        class="footer-btn"
        on:click={() => {
          userDelete = EMPTY;
        }}>확인</PrimaryButton
      >
    </div>
  </ModalFooter>
</ComposedModal>

<style>
  .user-type-label {
    font-weight: bold;
  }
  .user-info-table {
    width: 100%;
    border-collapse: collapse;
  }
  .user-info-table th,
  .user-info-table td {
    padding: 12px 18px;
    text-align: left;
  }
  .user-info-table th {
    background: #e5e5e5;
    font-weight: bold;
  }
  .user-info-table td {
    background: #fafafa;
    border-bottom: 1px solid #e5e5e5;
  }
  .user-profile-table {
    width: 100%;
    border-collapse: collapse;
  }
  .user-profile-header {
    background: #e5e5e5;
    font-weight: bold;
    padding: 12px 18px;
  }
  .user-profile-row {
    padding: 16px 18px;
  }
  .footer-actions {
    display: flex;
    justify-content: flex-end;
  }

  .footer-btn {
    width: 30%;
    min-width: 120px;
    max-width: 200px;
  }
</style>
