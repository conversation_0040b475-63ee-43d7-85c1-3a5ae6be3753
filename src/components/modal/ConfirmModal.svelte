<script>
  import { PrimaryButton } from 'src/components/common/button';
  import { createEventDispatcher } from 'svelte';
  export let dialog;
  const dispatch = createEventDispatcher();
</script>

<dialog bind:this={dialog} on:close>
  <div class="dialog-container">
    <div class="dialog-content">
      <h3 class="dialog-title">유저를 삭제하시겠습니까?</h3>
      <br />
      <p class="dialog-content-text">
        삭제된 유저는 더 이상 루나에 로그인할 수 없습니다. <br />
        단, 해당 유저가 생성한 예약/발권 내역은 그대로 유지됩니다. <br />
        정말 삭제하시겠습니까?
      </p>
    </div>
    <div class="dialog-footer-actions">
      <PrimaryButton
        kind="danger"
        style="flex: 1; min-height: 40px"
        on:click={() => {
          dialog.close();
          dispatch('confirm-delete-user');
        }}>유저 삭제</PrimaryButton
      >
      <PrimaryButton kind="secondary" style="flex: 1; min-height: 40px" on:click={() => dialog.close()}
        >취소</PrimaryButton
      >
    </div>
  </div>
</dialog>

<style>
  dialog {
    min-width: 32em;
    border-radius: 0.2em;
    border: none;
    padding: 0;
    background: var(--cds-ui-01, #f4f4f4);
  }
  dialog::backdrop {
    background: rgba(0, 0, 0, 0.3);
  }

  dialog[open] {
    animation: zoom 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  .dialog-title {
    font-size: 1.25rem;
  }

  .dialog-content {
    padding: 1rem 1rem 0 1rem;
  }

  .dialog-content-text {
    padding-right: 0;
    margin-bottom: 3rem;
    font-size: 0.875rem;
  }

  .dialog-footer-actions {
    display: flex;
    width: 100%;
  }
  @keyframes zoom {
    from {
      transform: scale(0.95);
    }
    to {
      transform: scale(1);
    }
  }
  dialog[open]::backdrop {
    animation: fade 0.2s ease-out;
  }
  @keyframes fade {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
</style>
