.app-header {

  .bx--header__menu-bar {
    position: relative;
    .bx--search-input {
      text-transform: uppercase;
    }
    .search-notify {
      border-radius: 8px;
      position: absolute;
      top: 100%;
      padding: 2px;
      width: 100%;
      color: #da1e28;
      p {
        font-size: 14px;
      }
    }
  }
  .bx--header {
    z-index: var(--z-super);
    padding: 0 40px;
    background-color: var(--app-header-bg-color) !important;
    height: var(--app-header-h);
    border-bottom: #ccc3c3 1px solid;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    .bx--header__name {
      width: 0;
      padding: 0;
      margin: 0;
    }

    .bx--header__nav {
      display: block !important;
    }
  }
  .logo-box {
    width: 160px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .logo-icon-less {
      display: none;
    }
    &:hover {
      cursor: unset;
    }
  }
  @media (max-width: 900px) {
    .bx--search-input {
      padding-right: 0 !important;
    }
    .logo-box {
      width: 40px;
      position: relative;
      .logo-icon {
        display: none;
      }
      .logo-icon-less {
        height: 36px !important;
        padding: 4px;
        display: block;
      }
    }

    .bx--header {
      padding: 0 0px !important;
    }
    .userID .bx--header__menu-item {
      width: 80px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 0;
    }
    .bx--header__menu-item {
      background: #000;
    }
  }
  .bx--header__nav::before {
    background-color: #e0e0e0;
    width: 1px;
    height: 24px;
  }
  .bx--header__nav.userID .bx--header__menu-item {
    cursor: default !important;
  }
  .bx--header__nav.userID::before {
    background-color: transparent;
  }
  .bx--search {
    input {
      outline: none;
      border: none;
      ::placeholder,
      :-ms-input-placeholder,
      ::-webkit-input-placeholder {
        color: #a8a8a8;
      }
    }
  }
  .bx--search-close {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    border: none !important;
    outline: none !important;
    margin-top: 2px;
    &:hover {
      background-color: transparent;
      border: none !important;
      outline: none !important;
    }
    &::before {
      display: none;
    }
  }
  .bx--header__menu li:hover {
    background: white !important;
  }

  .bx--header__menu li {
    background-color: white;
  }
  .bx--header__menu-item {
    color: black;
    background: white;
    cursor: pointer;
    &:hover {
      background: white !important;
      color: black !important;
    }
    &:not(:hover) {
      background: white !important;
      color: black !important;
    }
  }
  .bx--header__menu-title[aria-expanded='true'] {
    background: white !important;
    color: black !important;
    > .bx--header__menu-arrow {
      fill: black;
    }
  }
  .bx--header__menu-title:hover > .bx--header__menu-arrow {
    fill: black;
  }

  .toast-header {
    position: absolute;
    top: 36px;
    right: 0px;   
  }
}
#modal-search-pnr,
#modal-error-void-refund,
#modal-separation-passengers,
#modal-cancellation-request,
.modal-hidden-btn {
  .bx--btn.bx--btn--secondary:first-child {
    visibility: hidden;
  }
}

#modal-separation-passengers {
  padding: 0;
  .pointer-none {
    .bx--checkbox-label {
      pointer-events: none;
    }
  }
  .bx--data-table-container {
    margin-top: 24px;
  }
  .bx--modal-footer {
    padding: 0 !important;
    height: 40px;
  }
  button.bx--btn {
    height: 40px !important;
    min-height: auto !important;
    padding: 0;
    display: flex;
    align-items: center;
    padding-left: 16px;
  }
}

#modal-cancellation-request {
  .content-layout {
    padding: 24px 0 0 0;
    .total-amount {
      display: flex;
      padding: 7px 16px;
      flex-direction: row;
      justify-content: center;
      gap: 16px;
      flex: 1 0 0;
      box-shadow: 0px 1px 0px 0px #e0e0e0 inset;
      .title-total {
        font-weight: 600;
        display: flex;
        height: 32px;
        flex-direction: column;
        flex: 1 0 0;
        align-items: flex-start;
        justify-content: center;
      }
      .amount {
        display: flex;
        padding: 7px 16px;
        flex-direction: column;
        justify-content: center;
        align-items: flex-end;
        gap: 16px;
        flex: 1 0 0;
      }
    }
  }

  .bg-e0e0e0 {
    background-color: #e0e0e0;
  }
}
