$w-min: 1280px;
$w-max: 1920px;
$w-endpoint-1446: 1446px;

@mixin center-content-flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
@mixin center-content-flex-y {
  display: flex;
  align-items: center;
}
@mixin center-content-flex-c {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

// with parent position relative
@mixin center-content-absolute {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin minScreen {
  @media (max-width: #{$w-min - 1px}) {
    @content;
  }
}

@mixin middleScreen {
  @media (min-width: #{$w-min}) and  (max-width: #{$w-max - 1px}) {
    @content;
  }
}

@mixin maxScreen {
  @media (min-width: #{$w-max}) {
    @content;
  }
}
@mixin endpoint_1446 {
  @media (min-width: #{$w-min}) and  (max-width: #{$w-endpoint-1446 - 1px}) {
    @content;
  }
}

*::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}
*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background-color: #c4c4c4;
  border-radius: 20px;
}

.color-outstand-cell {
  color: #0f62fe;
  cursor: pointer;
}

.g-cell-text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.g-screen-transparent-isnot-event {
  background-color: transparent;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: var(--z-super);
}

.g-button {
  background-color: transparent;
  outline: unset;
  border: unset;
  cursor: pointer;
}

.g-visible-collapse {
  visibility: collapse;
}
.g-visible-hidden{
  display: none;
  // visibility: hidden;
}

.g-cursor-unset {
  cursor: unset;
}

.g-custom-loading{
  .bx--loading{
    width: 50px;
    height: 50px;
  }
}

.g-custom-loading-button .bx--inline-loading {
  .bx--inline-loading__text {
    color: white !important;
    font-size: 14px;
  }
}
.g-wrapper-toolbar-button{
  display: flex;
  gap: 2px;
}

//custom css carbon

.bx--date-picker-input__wrapper {
  .bx--date-picker__input {
    background-color: white;
  }
  .bx--date-picker__calendar {
    background-color: white;
  }
  .numInput {
    background-color: white;
  }
}
.bx--multi-select,
.bx--text-input {
  background-color: white;
}
.bx--multi-select {
  .bx--list-box__menu {
    background-color: white;
  }
}
.bx--multi-select:hover {
  background-color: #e5e5e5;
}
.bx--modal {
  .bx--modal-container {
    .bx--modal-footer {
      height: 40px;
      gap: 1px;
      .bx--btn {
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0 0 0 16px;
        min-height: unset;
      }
    }
  }
  .bx--modal-content:focus {
    outline: none;
  }
}
.app-page-content {
  padding: var(--app-header-h) 0 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  #booking_content {
    .bx--data-table--sticky-header {
      max-height: calc(100vh - 492px);
      transition: max-height 0.5s ease-in-out;
    }
  }
  #ocn_content {
    .bx--data-table--sticky-header {
      max-height: calc(100vh - 536px);
      transition: max-height 0.5s ease-in-out;
    }
  }
  #drsList, #userList {
    .bx--data-table--sticky-header {
      max-height: calc(100vh - 500px);
      transition: max-height 0.5s ease-in-out;
    }
  }

  &.btn-arrow-top-active {
    #booking_content {
      .bx--data-table--sticky-header {
        max-height: calc(100vh - 420px);
      }
    }
    #ocn_content {
      .bx--data-table--sticky-header {
        max-height: calc(100vh - 464px);
        transition: max-height 0.5s ease-in-out;
      }
    }
    #drsList {
      .bx--data-table--sticky-header {
        max-height: calc(100vh - 428px);
      }
    }
  }
}

.bx--text-input {
  outline: none !important;
  &:disabled{
    border-bottom: 1px solid var(--cds-border-strong, #8d8d8d) !important;
    background-color:white !important;
    opacity: 0.5;
  }
}
.bx--text-input__field-wrapper[data-invalid='true'] {
  padding-right: 0;
  .bx--text-input__invalid-icon {
    display: none;
  }
  .bx--text-input--invalid {
    outline: none !important;
    padding-right: 0;
  }
  input {
    width: 100%;
  }
}

.bx--dropdown--disabled {
  border-bottom: 1px solid var(--cds-border-strong, #8d8d8d) !important;
  background-color:white !important;
  opacity: 0.5;
}

.bx--text-input__field-outer-wrapper {
  position: relative;
  .bx--form-requirement {
    position: absolute;
    top: 100%;
  }
}


.bx--dropdown__wrapper{

  .bx--list-box[data-invalid='true']{
    outline: none !important;
    .bx--list-box__invalid-icon{
      display: none;
    }
    .bx--list-box__field{
      position: relative;
    }
  }
  .bx--form-requirement{
    position: absolute;
    outline: none !important;
    padding-right: 0;
  }
}


#default-btn {
  all: unset;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}



.bx--list-box__wrapper{
  .bx--list-box__field{
    padding-right: 0 !important;
  }
  input{
    padding-right: 16px !important;
  }
  .bx--list-box__menu-icon{
    right: 8px;
  }
  .bx--list-box__selection{
    right: 28px;
  }
  .bx--list-box__menu-item__selected-icon{
    right: 12px;
  }
}

.bx--label.bx--label--disabled {
  color: var(--cds-text-02, #525252);
}


.bx--date-picker__input:disabled{
border-bottom: 1px solid var(--cds-border-strong, #8d8d8d) !important;
}


.g-custom-table{
  tbody td {
    background-color: #ffffff ;
  }
  .table-toolbar {
    width: 100%;
    display: flex;
    align-items: center;
    .wrapper-title{
     .title-content{
      padding: 0px 12px;
     }
    }
    .wrapper-actions-button{
      display: flex;
      flex: 1;
      flex-direction: row-reverse;
      button{
        padding: 0 12px;
        min-width: 120px;
      }
    }
  }
}

#app-toast-notification{
  .toast-content {
    z-index: var(--z-toast);
    position: relative;
    text-shadow: 0 -1px 1px rgba(0, 0, 0, 0.2);
  }
  .toast-content:hover {
    background-color: #cccccc;
  }
}



#paymentInformationTable {
  .voucher-box {
    display: flex;
    padding: 16px;
    gap: 24px;
    .voucher-cash {
      width: 120px;
    }
  }
  .bx--radio-button__label {
    justify-content: start;
  }
  .voucher-input {
    margin-top: 10px;
    display: flex;
    width: 160px;
    margin-left: 16px;
  }
  .notify {
    margin-top: 4px;
    margin-left: 24px;
    color: red;
  }
  .bx--data-table {
    tr td:first-child {
      background-color: #cacaca;
    }
  }
}
button:disabled{
  cursor: not-allowed !important;
}


.btn-raw {
  background-color: unset;
  outline: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}