#expand-check-box.expand {
  padding: 0;
  min-height: unset;
  min-width: unset;
  .expand__head {
    height: 40px;
    background-color: #e0e0e0;
    width: 100%;
    text-align: start;
    padding: 0 20px;
    display: flex;
    align-items: center;
    border: none;
    cursor: pointer;
    border-bottom: 1px solid #8d8d8d;
    p {
      flex: 1;
    }
    .expand__head__icon {
      background-color: transparent;
      transition: transform 0.2s  ease-in-out ;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .expand__head--show{
    .expand__head__icon{
        transform: rotate(180deg) ;
    }
  }
  .expand__options {
    padding: 4px 8px;
    li {
      padding: 2px 8px;
      .bx--checkbox-wrapper {
        gap: 8px;
        display: flex;
        justify-content: center;
        label span {
          padding-left: 8px;
        }
        .labelText {
          font-size: 14px;
        }
      }
    }
  }
  .bx--tile.bx--tile--expandable {
    min-height: unset;
    min-width: unset;
    padding: 0;
    background-color: white;
    &:hover {
      background-color: white;

    }
  }
  .bx--tile__chevron {
    display: none;
  }
  .bx--tile--clickable:focus,
  .bx--tile--clickable:focus,
  .bx--tile--expandable:focus {
    outline: none !important;
  }
}
