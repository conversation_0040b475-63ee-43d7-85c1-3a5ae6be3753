:root {
  --app-bg-color-primary: #ffffff;

  --app-header-bg-color: #ffffff;
  --app-header-h: 48px;
  --app-ticket-info-m-top: 135px;

  --table-header-color:#e0e0e0;
  --table-cell-color:#ffffff ;
  --table-cell-border-color:#dddddd;

  --z-default: 0;
  --z-low: 10;
  --z-medium: 100;
  --z-very: 1000;
  --z-super: 9999;
  --z-toast: 10000;

}

* {
  box-sizing: border-box;
}

body {
  padding: 0;
  border: 0;

  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-track {
    background: #ffffff;
  }

  overflow-y: hidden;
}

   
ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.date-focus, .date-focus:hover {
  color: #ffffff !important;
  background-color: #0f62fe;
}

.tooltip {
  padding: 5px;
  line-height: 1.4;
  font-size: 12px;
}

.comboBoxSQAY div.bx--list-box__selection {
  display: none;
}

@import "./reservation-management-report";
@import "./modal"