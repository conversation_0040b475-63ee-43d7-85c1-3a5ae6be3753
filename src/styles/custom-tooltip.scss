
.svooltip {
    border-radius: 2px;
    --svooltip-text:white;
    background-color: #393939;
    padding: 2px;
    z-index: var(--z-super);
}

.svooltip-arrow{
    background-color: #393939;
}

.svooltip[data-placement='top'] {
	transform-origin: bottom center;
}
.svooltip[data-placement='top-start'] {
	transform-origin: top left;
    transform: translateX(12px) !important;
    transition: unset !important;
  

}
.svooltip[data-placement='top-end'] {
	transform-origin: bottom right;
}

.svooltip[data-placement='bottom'] {
	transform-origin: top center;
}
.svooltip[data-placement='bottom-start'] {
	transform-origin: top left;
}
.svooltip[data-placement='bottom-end'] {
	transform-origin: top right;
}

.seat-tooltip {
    padding: 10px 10px 10px 30px;
    list-style-type: square;
    line-height: 1.4;
}