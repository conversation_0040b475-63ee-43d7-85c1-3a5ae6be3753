.credit-card-form {
  padding: 0 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.form-item-with-number {
  position: relative;
}

.form-field {
  flex: 1;
}

.form-row {
  display: flex;
  gap: 1rem;
}

.form-item-half {
  flex: 1;
}

.expiry-date-container {
  display: flex;
  flex-direction: column;
}

.expiry-date-label {
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
}

.expiry-date-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.expiry-separator {
  font-size: 1.25rem;
  font-weight: bold;
}

.footer-container {
  width: 100%;
  display: flex;
  justify-content: right;
}

@media (max-width: 600px) {
  .form-row {
    flex-direction: column;
  }
}

.error-message {
  color: red;
  font-size: 11px;
}