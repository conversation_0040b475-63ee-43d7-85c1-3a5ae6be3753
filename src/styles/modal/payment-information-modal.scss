#PassengerInformationModal {
  footer {
    width: 100%;
    display: flex;
    flex-direction: row-reverse;
  }
  .action-button {
    flex: 0 0 33.33%;
  }
  @media (min-width: 82rem) {
    .bx--modal-container--lg {
      width: 95% !important;
    }
  }

  @media (min-width: 66rem) {
    .bx--modal-container--lg {
      margin-top: 50px;
      max-height: 94% !important;
    }
  }
  .bx--modal-content {
    padding-top: 0;
    padding-bottom: 0;
    margin-bottom: 0;
  }
  .wrapper-layout-content {
    padding: 12px 0;
 
  }

  .paymentInformationTable {
    .voucher-box {
      display: flex;
      padding: 16px;
      gap: 24px;
      .voucher-cash {
        width: 120px;
      }
    }
    .bx--radio-button__label {
      justify-content: start;
    }
    .voucher-input {
      margin-top: 10px;
      display: flex;
      width: 160px;
      margin-left: 16px;
    }
    .notify {
      margin-top: 4px;
      margin-left: 24px;
      color: red;
    }
    .bx--data-table {
      tr td:first-child {
        background-color: #cacaca;
      }
    }
  }
  .amountInformationTable {
  }
  .seatManageTable,
  .baggageManageTable {
    .no-selected-notify {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 96px;
      background-color: #ffffff;
    }
    .selected-action {
      padding: 16px 0;
      display: flex;
      justify-content: end;
    }
  }

  .grand-total-fare {
    background: #e0e0e0;
    text-align: right;
    padding: 5px 10px;
    font-size: 16px;
    font-weight: bold;
  }

  .passenger-information {
    .passenger-information__box + .passenger-information__box{
      margin-top: 24px;
    }
    .passenger-information__box {
      .passengerInformationItem {
        .passengerInformationItem-layout-flex {
          display: flex;
          align-items: center;
        }
        .passengerInformationItem__header {
          height: 36px;
          h6 {
            min-width: 200px;
            padding-left: 16px;
          }
          gap: 4px;
          background-color: #e0e0e0;
        }
        .passengerInformationItem-tag {
          display: flex;
          gap: 4px;
          align-items: center;
          font-weight: bold;
          height: 36px;
          padding: 8px;
          border-radius: 4px;
        }
        .passengerInformationItem__mobile-phone {
          align-items: end;
          gap: 8px;
        }
        .passengerInformationItem__ffn-airlines {
          align-items: end;
          gap: 8px;
        }
        .passengerInformationItem__condition {
          margin-top: 20px;
          align-items: end;
          gap: 16px;
          .item-w-132 {
            width: 132px;
          }
          .item-w-200 {
            width: 200px;
          }
        }
      }
    }
    .passenger-information__footer {
      display: flex;
      justify-content: end;
      margin-top: 16px;
      .contact-refusal {
        font-weight: bold;
      }
      > div {
        display: flex;
        align-items: center;
        padding: 0 16px;
        gap: 4px;
        .fix-position-tooltip {
          margin-top: 5px;
          
        }
        
      }
    }
  }
}


