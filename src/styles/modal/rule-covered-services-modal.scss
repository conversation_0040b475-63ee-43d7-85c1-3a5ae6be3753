#RuleCoveredServicesModal{
  footer {
      width: 100%;
      display: flex;
      flex-direction: row-reverse;
  }
  .check-button {
    flex: 0 0 33.33%;
    background-color: black !important;
  }

  .bx--modal-content{
    padding-top: 8px;
    padding-bottom: 8px;
    margin-bottom: 0;
}


  @media (min-width: 82rem){
      .bx--modal-container--lg {
          max-width: 95% !important;
          width: auto !important;
      }
  }
  @media (min-width: 66rem){
      .bx--modal-container--lg {
          margin-top: 50px;
          max-height: 94% !important;
          max-width: 95% !important;
          height: auto!important;
          width: auto !important;
      }
  }
  #master-table{
    .bx--data-table {
      min-width: 860px;  
      width:100%;
      ul {
        margin-left: 24px;
      }
      ul li {
        padding-left: 8px;
        list-style: '•';
        font-weight: 400;
        font-size: 14px;
        &:first-child {
          margin-top: 8px;
        }
      }
      ul li.hidden{
        visibility: collapse;
      }
      padding: 16px !important;
      h2 {
        font-size: 14px;
        font-weight: 700;
      }
      thead {
        min-height: 94px;
        background-color: #ffffff;
        position: relative;
      }
  
      th,
      td {
        min-width: 200px !important;
        background-color: transparent;
        padding-top: 16px;
        padding-bottom: 16px;
        &:not(:first-child) {
          padding-left: 0 !important;
          padding-right: 0 !important;
        }
      }    
    }
  
    hr {
      border: none; 
      background-color: #ccc; 
      height: 1px; 
      margin: 20px 0; 
    }
   
}
}


