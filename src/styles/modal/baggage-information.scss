#baggage-information {
    .bx--tabs__nav {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(4, minmax(0, 1fr));
        li {
            width: 100%;
            a {
                width: 100%;
            }
        }
    }

    .ptc-cell {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .baggage-cell--amount {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        svg {
            cursor: pointer;
            margin-left: -15px;
        }
    }

    .baggage-tag {
        color: #ffffff;
        background-color: #121619;
        border-radius: 20px;
        font-size: 12px;
        padding: 2px 6px;
    }

    .table-empty {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
    }

    .bx--data-table td {
        border-top: none;
        background-color: #f0f0f0;
    }

    .baggage-footer {
        padding: 15px;
        background-color: #e0e0e0;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        span {
            margin-left: 10px;
            font-weight: bold;
            font-size: 16px;
        }
    }

    footer {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        button {
            flex: 0 0 33.33%;
            width: 100%;
        }
    }

    .baggage-wrapper {
        display: flex;
        gap: 2px;
        height: 100%;

        .baggage-container--left {
            flex: 1;
        }

        .baggage-container--right {
            flex: 1;
            margin-top: 2px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
    }

    .bx--tab-content {
        padding: 0;
    }

    .col-span-3 {
        grid-column: span 3 / span 3;
    }

    .col-span-4 {
        grid-column: span 4 / span 4;
    }

    .col-span-2 {
        grid-column: span 2 / span 2;
    }

    .col-span-5 {
        grid-column: span 5 / span 5;
    }

    .baggage--right-head {
        display: grid;
        background-color: #e0e0e0;
        grid-template-columns: repeat(12, minmax(0, 1fr));
        font-weight: 600;
        height: 48px;
        align-content: center;
        justify-content: center; 
        padding: 15px 5px;
        div.text-align-r {
            text-align: right;
        }
        div.text-align-l {
            text-align: left;
        }
    }

    .baggage--right-middle {
        overflow: auto;
        display: flex;
        flex-direction: column;
        gap: 5px;
        max-height: 250px;
        .right-middle-item {
            display: grid;    
            background-color: #ffffff;
            border: 2px solid #e0e0e0;               
            grid-template-columns: repeat(12, minmax(0, 1fr));
            padding: 15px 5px;
            cursor: pointer;
            div.text-align-r {
                text-align: right;
            }
            div.text-align-l {
                text-align: left;
            }
        }
        .right-middle-item.selected {
            border-color: #0F62FE;
        }
        
    }

    .baggage--right-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #e0e0e0;
        padding: 10px 15px;
        font-weight: 600;
    }

    .btn-recalculate {
        flex: 1;
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        margin-bottom: 2rem;
    }

    .right-middle-item.selected {
        border-color: #0F62FE;
    }

    .baggage-container {
        min-height: 40vh;
        display: flex;
        flex-direction: column;

        .baggage-wrapper{
            flex: 1
        }
    }

    .btn-delete {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    @media (min-width: 82rem) {
        .bx--modal-container--lg {
            width: 65%;
        }
    }

    @media (max-width: 130rem) {
        .bx--modal-container--lg {
            width: 90%;
        }
    }


}