#changePassengerInformation {
  .description {
    margin: 16px 0;
  }

  .check-button {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .add.disabled {
    color: #c6c6c6;
    cursor: not-allowed;
  }

  .passenger-information-container {
    margin-top: 30px;

    .passenger-information-box {
      margin-top: 8px;
      display: flex;
      align-items: center;
      gap: 32px;
    }

    .passenger-information-dropdown {
      display: flex;
      align-items: center;
      gap: 16px;
      p {
        padding: 0;
      }
    }

    .passenger-information-method {
      display: flex;
      align-items: center;
      gap: 16px;

      .passenger-information-method-tooltip {
        display: flex;
        align-items: center;
        gap: 8px;

        .btn {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        p {
          flex-shrink: 0;
          padding-right: 0;
        }
      }
    }

    .text-danger {
      color: #da1e28;
    }
  }

  .edited {
    background-color: #c4d5ef;
  }

  .checkbox-input-container {
    display: flex;
    align-items: center;

    .input-country-dial-code {
      margin-right: 8px;
      max-width: 65px;
    }
  }

  .error-text-input {
    color: red;
    font-size: 12px;
  }

  .confirmed {
    background-color: #bed0f1 !important;
    border-color: #bed0f1 !important;
  }

  .confirmed:hover {
    background-color: #bed0f1 !important;
  }

  .selected-for-delete {
    background-color: #ffd7d7 !important;
    border-color: #ff8080 !important;
  }

  .confirmed.selected-for-delete {
    background-color: #ffd7d7 !important;
    border-color: #ff8080 !important;
  }

  .selected-for-delete:hover {
    background-color: #ffbfbf !important;
  }

  footer {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    button {
      flex: 0 0 33.33%;
      width: 100%;
    }
  }

  @media (min-width: 82rem) {
    .bx--modal-container--lg {
      width: 70%;
    }
  }
}
