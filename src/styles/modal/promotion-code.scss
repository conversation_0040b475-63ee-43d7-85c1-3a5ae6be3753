#modal-pc {
  .bx--modal-container {
    min-width: 850px;
  }

  .bx--modal-container {
    background-color: #ffffff;
  }

    .bx--list-box__menu {
      position: sticky !important;
    }

  footer {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    button {
        flex: 0 0 33.33%;
        width: 100%;
    }
  }
  
  .modal-pc__container {
    .modal-pc__item {
      .modal-pc__item--header {
        background-color: #e0e0e0;
        padding: 12px;
        display: flex;
        align-items: center;
        gap: 10px;
        p,
        button {
          color: #000000;
          font-weight: 600;
          padding: 0;
        }
      }
      .modal-pc__item--content {
        background-color: #f4f4f4;
        padding: 12px;
        min-height: 100px;
        max-height: 400px;
        overflow: scroll;
        .form-wrapper:last-child {
          fieldset {
            margin-bottom: 0;
          }
        }
        .form-wrapper {
          display: flex;
          flex-wrap: nowrap;
        }

        .form-wrapper:last-child {
          .pc-account__group {
            padding-top: 24px;
          }
        }

        .form-group-pc {
          display: flex;
          align-items: center;
          gap: 15px;
          button {
            margin-top: 24px;
          }
        }

        .pc-account__group {
          padding: 0 15px 0 15px;
          display: flex;
          align-items: center;
          gap: 15px;

          .pc-account__item {
            min-width: 100px;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 6px 12px;
            color: #000000;
            background-color: #cdd3da;
          }

          p {
            padding-right: 0 !important;
          }
        }
      }
    }
  }
}
