#purchase-seats {
  --tabs-cols: 4;
  .bx--tabs__nav {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(var(--tabs-cols), minmax(0, 1fr));
    li {
      width: 100%;
      a {
        width: 100%;
      }
    }
  }

  .seat,
  .seat-class--color {
    @include center-content-flex;
    flex-shrink: 0;
    width: 18px;
    height: 18px;
  }

  .seat-class--icon {
    @include center-content-flex;
  }

  .seats-map {
    .seats-map--class {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 25px;
      border-bottom: 1px solid #e0e0e0;
      padding: 0 15px 15px 15px;
      .seat-class {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
    .seats-map--area {
      height: 60vh;
      display: flex;
      gap: 20px;
      .seats-area--left {
        position: relative;
        flex: 3;
        overflow: auto;
        display: flex;
        .seats-area--container {
          width: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          .seat-empty {
            background-color: #cccccc;
            border-radius: 2px;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
        .seat-icon--left,
        .seat-icon--right {
          position: absolute;
        }

        .seat-icon--left {
          top: 50%;
          left: 10%;
        }
        .seat-icon--right {
          top: 50%;
          right: 10%;
        }
      }
      .seats-area--right {
        flex: 2;
        margin-top: 15px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        .seats-area--right-head {
          display: grid;
          background-color: #e0e0e0;
          padding: 10px 15px;
          grid-template-columns: repeat(7, minmax(0, 1fr));
          font-weight: 600;
        }

        .seats-area--right-middle {
          overflow: auto;
          display: flex;
          flex-direction: column;
          gap: 10px;
          .right-middle-item {
            display: grid;
            background-color: #ffffff;
            border: 2px solid #e0e0e0;
            grid-template-columns: repeat(7, minmax(0, 1fr));
            padding: 15px;
            cursor: pointer;
            position: relative;

            :nth-child(3) {
              display: flex;
              align-items: center;
              justify-content: space-between;
              position: relative;
            }
          }
          .right-middle-item.selected {
            border-color: #0f62fe;
          }
        }

        .seats-area--right-bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          background-color: #e0e0e0;
          padding: 10px 15px;
          font-weight: 600;
        }
      }
    }

    .col-span-3 {
      grid-column: span 3 / span 3;
    }
    .passenger-name {
      text-align: start;
    }
    .seat-info {
      text-align: start;
    }
  }

  footer {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    button {
      flex: 0 0 33.33%;
      width: 100%;
    }
  }

  .seats-empty {
    @include center-content-flex;
    width: 100%;
    p {
      text-align: center;
      padding: 0;
    }
  }
  .btn-clean {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 3%;
  }
  .calculate-price {
    display: flex;
    flex-direction: row-reverse;
  }

  .bx--tab-content {
    padding: 1rem 0;
    background: #f0f0f0;
  }

  .bx--modal-header {
    padding: 1.2rem;
  }

  @media (min-width: 82rem) {
    .bx--modal-container--lg {
      width: 70%;
    }
  }
  .layout-seat {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }
  #seat-table {
    table {
      thead {
        background-color: transparent;
        th {
          background-color: transparent;
          padding: 0;
          text-align: center;
        }
      }
      tbody {
        background-color: transparent;
        tr {
          background-color: transparent;
          border: none;
          td {
            background-color: #0f62fe;
            border: none;
            padding: 1px;
            margin: 0;
            &:not(:hover) {
              background-color: transparent;
            }
            &:hover {
              background-color: transparent;
            }
          }
        }
      }
    }
    .seat-cell {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      button.seat-cell-content {
        height: 20px;
        aspect-ratio: 1/1;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: transparent;
        border: 1px solid #cccccc;
        cursor: pointer;
        padding: 0;
      }
      button.seat-cell-content:disabled {
        cursor: not-allowed;
      }
      .seat-exit-row-right {
        position: absolute;
        right: -40px;
      }
      .seat-exit-row-left {
        position: absolute;
        left: -80px;
      }
    }
    .seat-cell--hidden {
      visibility: hidden;
    }
  }
  #seat-ptc-table {
    table {
      tr {
        th,
        td {
          &:first-child {
            display: none;
          }
        }
        &.bx--data-table--selected {
          border: 2px solid #0f62fe !important;
        }
      }      
    }
  }
}
