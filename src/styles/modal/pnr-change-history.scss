#modal-pnr-change-history {
  @media (min-width: 66rem) {
    .bx--modal-container--lg {
      width: 80%;
      max-height: 85%;
    }

    table.bx--data-table--sticky-header {
      max-height: calc(100vh - 25rem);
    }
  }

  p {
    padding: 0;
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: start;
    margin-bottom: 1rem;
    gap: 1rem;

    p {
      font-size: 1rem;
    }
  }

  .bx--modal-header {
    h3 {
      font-weight: bold;
      font-size: x-large;
    }
  }

  .bx--modal-footer {
    display: none;
  }

  .bx--data-table--sticky-header {
    max-height: calc(100vh - 20rem);
  }

  .bx--data-table {
    thead {
      tr {
        th:nth-child(1),
        th:nth-child(3),
        th:nth-child(2),
        th:nth-child(4),
        th:nth-child(5) {
          flex: 1;
        }

        th:nth-child(6) {
          flex: 5;
        }
        th {
          text-align: center;
          padding-left: 0;

          .bx--table-header-label {
            flex: 1;
            text-align: center;
          }
        }
      }
    }

    tbody {
      tr {
        height: auto;

        td {
          padding: 0.5rem 0;
        }
        td:nth-child(3),
        td:nth-child(2),
        td:nth-child(4),
        td:nth-child(5) {
          justify-content: center;
        }

        td:nth-child(1),
        td:nth-child(3),
        td:nth-child(2),
        td:nth-child(4),
        td:nth-child(5) {
          flex: 1;
        }

        :nth-child(6) {
          flex: 5;
        }
      }

      tr:nth-child(even) td {
        background: #f0f0f0;
      }
      tr:nth-child(odd) td {
        background: #ffffff;
      }
      tr:hover td,
      tr:hover th {
        background: var(--cds-layer-hover, #e5e5e5);
      }

      .detail-text {
        white-space: pre-line;
        color: var(--cds-interactive-01);
        text-align: left;
      }

      .change-date-time {
        text-align: left;
        padding-left: 1rem;
      }
    }
  }
}
