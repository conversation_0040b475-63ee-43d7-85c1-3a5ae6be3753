#add-new-user-modal {
  .user-type-label {
    font-weight: bold;
  }
  .user-info-table {
    width: 100%;
    border-collapse: collapse;
  }
  .user-info-table th,
  .user-info-table td {
    padding: 12px 18px;
    text-align: left;
  }
  .user-info-table th {
    background: #e5e5e5;
    font-weight: bold;
  }
  .user-info-table td {
    background: #fafafa;
    border-bottom: 1px solid #e5e5e5;
  }
  .user-profile-table {
    width: 100%;
    border-collapse: collapse;
  }
  .user-profile-header {
    background: #e5e5e5;
    font-weight: bold;
    padding: 12px 18px;
  }
  .user-profile-row {
    display: grid;
    padding: 16px 18px;
  }

  .btn-add-user {
    width: 320px !important;
  }

  .bx--radio-button-wrapper {
    .bx--radio-button__label {
      justify-content: unset;
    }
    span {
      font-weight: bold;
    }
  }

  .mobile-number-container {
    display: flex;
    justify-content: unset;
    gap: 16px;
    padding-bottom: 24px;

    .bx--form-item.bx--text-input-wrapper {
      &:nth-child(1) {
        max-width: 75px;
        .bx--form-requirement {
          width: 400px;
        }
      }

      &:nth-child(2) {
        max-width: 250px;
      }
    }
  }
}
