#modal-ffn {
  @media (min-width: 82rem) {
    .bx--modal-container--sm {
        width: 32%;
    }
  }

  @media (min-width: 120rem) {
    .bx--modal-container--sm {
      width: 22%;
    }
  }
  .bx--modal-container {
    background-color: #ffffff;
  }
  .modal-ffn__grid {
    padding: 0 !important;
  }

  .modal-ffn__header {
    padding: 16px;
    background-color: #e0e0e0;
    font-weight: 600;
    display: grid;
    grid-template-columns: repeat(5, minmax(0, 1fr));
    gap: 16px;

    :nth-child(2), :nth-child(3) {
      grid-column: span 2 / span 2;
    }
  }

  .modal-ffn__content {
    padding: 16px;
    background-color: #f4f4f4;
    display: flex;
    flex-direction: column;
    gap: 25px;

    .modal-ffn__item {
      display: grid;
      grid-template-columns: repeat(5, minmax(0, 1fr));
      gap: 16px;

      :nth-child(2), :nth-child(3) {
        grid-column: span 2 / span 2;
      }
    }
  }
  footer {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    button {
        flex: 0 0 33.33%;
        width: 100%;
    }
  }

  .ptc-center {
    display: flex;
    align-items: center;
    p {
      text-align: center;
    }
  }

  .group-btn-action {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .cb-not-clear + .bx--list-box__selection {
    display: none;
  }
}
