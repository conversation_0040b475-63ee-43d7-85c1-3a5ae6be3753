.form-reset-password {
  width: 320px;
  left: 20%;
  position: absolute;
  top: 30%;
  transform: translateY(-50%);
  .form-logo {
    display: flex;
    justify-content: center;
    align-items: center;
    transform: scale(2.2);
    margin-bottom: 52px;
  }
  .text-input {
    margin-bottom: 12px;
    input {
      background-color: white;
    }
  }
  .submit-input {
    width: 100% !important;
    height: 40px !important;
    min-height: auto;
    min-width: auto;
    justify-content: center;
    text-transform: capitalize;
    padding-left: 0;
    padding-right: 0;
  }

  .bx--text-input__field-wrapper[data-invalid='true'] .bx--text-input--invalid {
    padding-right: 2.5rem !important;
  }

  .bx--text-input__field-wrapper {
    input.hidden-eye ~ button.bx--text-input--password__visibility__toggle {
      display: none;
    }
  }
  
  .bx--fieldset {
    margin-bottom: 2.5rem;
  }
}
.toast-reset-password {
  z-index: var(--z-toast);
  position: absolute;
  right: 30px;
  min-height: 300px;
  .toast-content {
    position: relative;
    text-shadow: 0 -1px 1px rgba(0, 0, 0, 0.2);
  }
}

@media (max-width: 900px) {
  .form-reset-password {
    left: 0;
    top: 42%;
    width: calc(60vw);
    padding: 0 10vw;
  }
  .videoWrapper {
    transform: scale(2);
    width: 40vw;
    top: 0;
    height: 60vh;
    right: -16vw;
  }
}

.input-wrapper-svg {
  display: none;
}

.bx--fieldset {
  &.text-input-icon {
    position: relative;
    .input-wrapper-svg {
      position: absolute;
      right: 5px;
      bottom: 8px;
    }
  }

  &[data-invalid='true']:has(input:focus).text-input-icon {
    .input-wrapper-svg {
      display: block;
      svg:nth-child(1) {
        display: block;
      }

      svg:nth-child(2) {
        display: none;
      }
    }
  }

  &[data-invalid='false']:has(input:focus).text-input-icon {
    .input-wrapper-svg {
      display: block;
      svg:nth-child(1) {
        display: none;
      }

      svg:nth-child(2) {
        display: block;
      }
    }
  }
}
