.app-sign-in {
  background-color: #f3f3f3;
  width: 100vw;
  height: 100vh;
  position: relative;
  .videoWrapper {
    position: absolute;
    overflow: hidden;
    border-radius: 0 0 0 100%;
    top: 0;
    right: 0;
    width: 40vw;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .form-sign-in {
    width: 320px;
    left: 20%;
    position: absolute;
    top: 30%;
    transform: translateY(-50%);
    .form-logo {
      display: flex;
      justify-content: center;
      align-items: center;
      transform: scale(2.2);
      margin-bottom: 52px;
    }
    .text-input {
      margin-bottom: 12px;
      input {
        background-color: white;
      }
    }
    .submit-input {
      width: 100% !important;
      height: 40px !important;
      min-height: auto;
      min-width: auto;
      justify-content: center;
      text-transform: capitalize;
      padding-left: 0;
      padding-right: 0;
    }
  }
  .toast-sign-in {
    z-index: var(--z-toast);
    position: absolute;
    right: 30px;
    min-height: 300px;
    .toast-content {
      position: relative;
      text-shadow: 0 -1px 1px rgba(0, 0, 0, 0.2);
    }
  }
}

@media (max-width: 900px) {
  .app-sign-in {
    .form-sign-in {
      left: 0;
      top: 42%;
      width: calc(60vw);
      padding: 0 10vw;
    }
    .videoWrapper {
      transform: scale(2);
      width: 40vw;
      top: 0;
      height: 60vh;
      right: -16vw;
    }
  }
}
