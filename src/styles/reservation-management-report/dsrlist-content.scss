#dsr-list-wrapper {
  .form-wrapper {
    padding-top: 20px;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    .bx--date-picker--range .bx--date-picker-container,
    .bx--date-picker--range .bx--date-picker__input {
      width: 142px;
    }
    .wrapper-action {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: end;
      margin-top: -8px;
      button {
        height: 40px;
        min-height: unset !important;
        width: 160px;
      }
      button + button {
        margin-left: 16px;
      }
    }
    .m-l-32 {
      margin-left: 32px;
    }
    .w-160 {
      width: 160px;
    }
  }
  .color-outstand-cell-red {
    color: #da1e28;
  }
  .color-outstand-cell-blue {
    color: #0f62fe;
    cursor: pointer;
  }

  .text-totalFare {
    text-align: right;
    min-width: 100px;
    .align-money {
      width: 100%;
    }
  }
  .text-totalFare-header {
    text-align: right;
    min-width: 100px;
  }

  .table-toolbar {
    display: flex;
    background-color: transparent;
    .custom-button-download {
      margin-top: 7px;
      height: 40px;
      display: flex;
      justify-content: space-evenly;
      width: 160px;
      padding: 0 16px;
      span {
        flex: 1;
      }
    }
  }
  .dsr-list-table {
    margin-top: 46px;
    .bx--data-table-container {
      .bx--data-table {
        width: 100%;
        tbody tr td {
          background-color: white;
          &:nth-child(1) {
            max-width: 200px;
          }
          &:nth-child(2),
          &:nth-child(3) {
            max-width: 100px;
          }
          &:nth-child(7) {
            min-width: 320px;
          }
          &:nth-child(12),
          &:nth-child(13) {
            max-width: 120px;
          }
          &:nth-child(15) {
            max-width: 120px;
          }
        }
        thead tr th {
          &:nth-child(1) {
            max-width: 200px;
          }
          &:nth-child(2),
          &:nth-child(3) {
            max-width: 100px;
          }
          &:nth-child(7) {
            min-width: 320px;
          }
          &:nth-child(12),
          &:nth-child(13) {
            max-width: 120px;
          }
          &:nth-child(15) {
            max-width: 120px;
          }
        }
        @media (max-width: 1920px) {
          thead tr th {
            &:nth-child(7) {
              max-width: 200px;
            }
          }
          tbody tr td {
            &:nth-child(7) {
              min-width: unset;
              max-width: 200px;
            }
          }
        }
      }
    }
    .bx--pagination {
      background-color: white;
      .bx--select-input {
        background-color: white;
      }
    }
  }
  .loading-dsr-list {
    height: calc(100vh - 500px);
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .loading-icon {
      transform: scale(0.8);
    }
  }
}
