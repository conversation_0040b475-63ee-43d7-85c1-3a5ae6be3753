#table-management {
  tbody td {
    background-color: #ffffff;
  }
  .table-toolbar {
    height: 72px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    .title-table {
      padding-bottom: 4px;
    }
    button {
      width: 120px;
      padding: 8px 16px;
      height: 32px;
    }
  }
 
}
#table-management.table-management__ticket {
  tbody > tr > td:first-child {
    color: rgba(15, 98, 254, 1);
    cursor: pointer;
  }
}

#table-management.ocn-table td,
th {
  padding-right: 0 !important;
}

#table-management.reservation {
  .bx--row-padding [class*='bx--col'],
  .bx--col-padding {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  .button-box {
    display: flex;
    justify-content: flex-end;
  }
}
