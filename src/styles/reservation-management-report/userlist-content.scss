#user-list-wrapper {
  .form-wrapper {
    padding-top: 20px;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    .bx--date-picker--range .bx--date-picker-container,
    .bx--date-picker--range .bx--date-picker__input {
      width: 142px;
    }
    .wrapper-action {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: end;
      margin-top: -8px;
      button {
        height: 40px;
        min-height: unset !important;
        min-width: 160px;
      }
      button + button {
        margin-left: 16px;
      }
    }
    .m-l-32 {
      margin-left: 32px;
    }
    .w-160 {
      width: 160px;
    }
    .w-190 {
      width: 190px;
    }
    .w-320 {
      width: 320px;
    }
  }
  .color-outstand-cell-red {
    color: #da1e28;
  }
  .color-outstand-cell-blue {
    color: #0f62fe;
    cursor: pointer;
  }

  .text-center {
    text-align: center;
  }

  .email-text {
    color: var(--cds-link-01);
  }

  .text-totalFare {
    text-align: right;
    min-width: 100px;
    .align-money {
      width: 100%;
    }
  }
  .text-totalFare-header {
    text-align: right;
    min-width: 100px;
  }

  .table-toolbar {
    display: flex;
    background-color: transparent;
    .custom-button-download {
      margin-top: 7px;
      height: 40px;
      display: flex;
      justify-content: space-evenly;
      width: 160px;
      padding: 0 16px;
      span {
        flex: 1;
      }
    }
  }
  .user-list-table {
    margin-top: 46px;
    .bx--data-table-container {
      .bx--data-table {
        width: 100%;
        tbody tr td {
          background-color: white;
          &:nth-child(1) {
            max-width: 100px;
          }
        }
        thead tr th {
          &:nth-child(1) {
            max-width: 100px;
          }
        }
        @media (max-width: 1920px) {
          thead tr th {
            &:nth-child(7) {
              max-width: 200px;
            }
          }
          tbody tr td {
            &:nth-child(7) {
              min-width: unset;
              max-width: 200px;
            }
          }
        }
      }
    }
    .bx--pagination {
      background-color: white;
      .bx--select-input {
        background-color: white;
      }
    }
  }
  .loading-user-list {
    height: calc(100vh - 500px);
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .loading-icon {
      transform: scale(0.8);
    }
  }
}
