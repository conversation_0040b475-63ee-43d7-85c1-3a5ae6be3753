$paddingViewContentTableX: 24px;
$tabsContainerHeight: 48px;
$containerBg: #f4f4f4;

.title-table {
  width: 100%;
  display: table;
}

.title-table-content {
  display: table-cell;
  vertical-align: middle;
  padding-left: 0.5rem;
}

//__________
.app-page-content .app__info {
  @include center-content-flex-y;
  height: var(--app-ticket-info-m-top);
  padding: 0 46px;
  transition: height 0.6s ease 0s;
  .info-wrapper .title {
    @include center-content-flex;
    font-weight: 700;
    height: 36px;
    font-size: 28px;
    .parent-child-pnr {
      display: flex;
      padding: 0px 16px 7px 16px;
      align-items: flex-end;
      gap: 8px;
      align-self: stretch;
    }
    .parent-child-pnr .item {
      display: flex;
      padding: 2px 8px;
      align-items: flex-start;
      background-color: #cdd3da;
      border-radius: 16px;
      font-size: 12px;
    }

    .parent-child-pnr .selected {
      background-color: #a5d2f9;
      color: #0043ce;
    }
  }
  .info-wrapper .button {
    margin-top: 4px;
    @include center-content-flex;
    background-color: #da1e28;
    color: #ffffff;
    width: 136px;
    height: 32px;
    cursor: pointer;
    text-transform: uppercase;
    font-weight: 500;
  }
  .info-wrapper .pnr {
    font-size: 28px;
    font-weight: 700;
    color: #161616;
    height: 100%;
    padding: 0;
  }
}

.app-page-content .view-content-table {
  flex-grow: 1;
  overflow: hidden;
  position: relative;
  .bx--tabs {
    border-top: 0.5px solid #c6c6c6;
  }
  .btn-arrow-top {
    @include center-content-flex;
    cursor: pointer;
    position: absolute;
    right: 0;
    border: none;
    z-index: var(--z-low);
    height: $tabsContainerHeight;
    aspect-ratio: 1/1;
    background-color: black;
    transition: transform 0s ease 0s;
  }
  .bx--tabs.bx--tabs--container {
    background-color: rgba(188, 188, 188, 0.3);
    backdrop-filter: blur(18px);
    -webkit-backdrop-filter: blur(18px);
    position: relative;
    height: $tabsContainerHeight;
    padding: 0 $paddingViewContentTableX;
  }
  .bx--tab-navigate {
    background-color: red;
  }
  .bx--tab-content {
    padding: 0 $paddingViewContentTableX;
    height: calc(100vh - 96px - var(--app-ticket-info-m-top));
    overflow: overlay;
    overflow-y: scroll;
    background-color: $containerBg;
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-track {
      background: #ffffff;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #c4c4c4;
      border-radius: 20px;
    }
  }
  .tabs-container {
    width: var(--app-tab-width);
    .bx--tabs__nav-link {
      width: 100%;
      padding: 0 !important;
    }
    .tab-navigate {
      width: 100%;
      height: 100%;
      @include center-content-flex-y;
      padding: 0 16px;
      .tab-name {
        padding-left: 8px;
        flex-grow: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .btn-close {
        background-color: unset;
        outline: none;
        border: none;
        cursor: pointer;
      }
    }
  }
  .none-tab {
    display: none;
  }
}

.app-page-content.btn-arrow-top-active {
  --app-ticket-info-m-top: 60px;

  .bx--tab-content {
    max-height: calc(100vh - 96px - var(--app-ticket-info-m-top));
  }
  .app__info {
    justify-content: flex-start;
    .info-wrapper {
      @include center-content-flex;
    }
    .info-wrapper .button {
      background-color: unset;
      color: #da1e28;
      height: auto;
      margin-top: 0;
    }
    .info-wrapper .title {
      font-size: 14px;
      .pnr {
        font-size: 14px;
        margin-top: 0px;
      }
    }
  }
  .view-content-table {
    .btn-arrow-top {
      transform: rotate(180deg);
    }
  }
}

.app-page-content.isHiddenContent {
  display: none;
}

#modal-api-error {
  .bx--modal-footer {
    .bx--btn {
      background-color: #000000;
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }
}

//ocn-content

.bx--toast-notification__caption:empty {
  display: none;
}
.bx--toast-notification__subtitle {
  white-space: pre-line;
}

.app-bg-home {
  width: 100vw;
  height: 100vh;
  z-index: var(--z-super);
  background-color: #f5f0f0;
  transform: scale(1);
  background-repeat: repeat;
  // display: none;
}

.video-head-reservation {
  position: absolute;
  top: 0;
  right: 0;
  z-index: var(--z-medium - 1);
  width: 600px;
}

@media (max-width: 1200px) {
  .video-head-reservation {
    display: none;
  }
}

@import './ocn-content';
@import './booking-content';
@import './table-management';
@import './dsrlist-content';
@import './userlist-content';
@import './cancel-request-order';
@import './reservation-ticketing.scss'
