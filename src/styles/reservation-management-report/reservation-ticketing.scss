#reservation-ticketing-wrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 300vh;
  .condition {
    padding: 12px 20px;
  }
  
  .history{
    padding: 0px 20px 20px 20px;
    h4{
      padding-bottom: 8px;
    }
    ul li {
      display:  flex;
      align-items: center;
      gap: 8px;
    }
    li + li{
      padding-top: 8px;
    }
    
  }
  .sticky-container {
    position: sticky;
    top: 10px;
    .layout-warning{
      display: flex;
      flex-direction: row-reverse;
    }
  }
  .container {
    display: flex;
    gap: 8px;
    height: calc(100vh - 340px);
    overflow: hidden;
    .container__options-content {
      width: 180px;
      overflow: scroll;
    }
    .container__view-content {
      flex: 1;
    }
  }


}
.flightTable__btn-scroll-top{
  position: absolute;
   background-color: #0F62FE;
   z-index: var(--z-very);
   border: none;
   cursor: pointer;
   height: 32px;
   width: 32px;
   display: flex;
   justify-content: center;
   align-items: center;
   color: white;
   right: 40px;
   bottom: 56px;
}
.flightTable__btn-scroll-top--hidden{
  visibility: hidden;
}
.flightTable__actions-box{
  position: absolute;
  bottom: 8px;
  right: 38px;
  z-index: var(--z-very);
  display: flex;
  gap: 1px;
}

.loading-table-for-reservation-ticketing{
  height: calc(100vh - 540px);
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}