#booking-content-wrapper {
  padding-top: 20px;
  .bx--date-picker--range .bx--date-picker-container,
  .bx--date-picker--range .bx--date-picker__input {
    width: 142px;
  }
  .hide-label {
    label {
      visibility: hidden;
    }
  }
  .bx--multi-select__wrapper {
    min-width: 142px;
  }
  .ocn--w-160 {
    width: 160px;
  }
  .ocn--w-96 {
    width: 96px;
  }
  .ocn--w-104 {
    width: 104px;
  }
  .ocn--w-120 {
    width: 120px;
  }

  .ocn--w-150 {
    width: 150px;
  }

  .ocn--h-40 {
    height: 40px;
  }
  .ocn--ml-1 {
    margin-left: 1px;
  }
  .ocn--w-200 {
    width: 200px;
  }
  .p-b-14 {
    padding-bottom: 14px;
  }
  .box-flex-c {
    display: flex;
    justify-content: center;
    gap: 1px;
  }
  .btn-toggle {
    color: black;
    border: none;
    padding: 8px;
    cursor: pointer;
    &:hover {
      color: black;
      background-color: unset;
    }
    &:focus {
      border-color: unset;
      box-shadow: unset;
    }
  }
  .btn-toggle + .btn-toggle {
    padding-left: 0;
    margin-left: -2px;
  }

  #passenger-name {
    margin-right: 1px;
  }

  .form-wrapper {
    min-height: 60px;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    .input-wrapper {
      display: flex;
      margin-left: 20px;
      justify-content: space-between;
      width: 100%;
      max-width: 1200px;
    }
    .form-action {
      @include center-content-flex-c;
      display: flex;
      flex-direction: row-reverse;
      justify-content: end;
      align-items: end;
      margin-top: 24px;
      flex-grow: 1;
      .action__btn + .action__btn {
        margin-top: 8px;
        margin-right: 1px;
      }
    }
    .form-action .action__btn {
      @extend .ocn--w-160;
      @extend .ocn--h-40;
      min-width: auto;
      min-height: auto;
    }
    .form-toggle--datetime {
      position: absolute;
      top: 66px;
      z-index: var(--z-low);
      .bx--label {
        margin-top: 8px;
      }
      .datetime-wrapper {
        display: flex;
      }
      .btn-toggle-wrapper {
        display: flex;
        padding-top: 28px;
      }
    }
  }

  .booking-content-table.booking-content-table-mt120 {
    margin-top: 120px !important;
  }
  .booking-content-table {
    margin-top: 44px;
    .bx--data-table-container {
      .bx--data-table {
        tbody tr td {
          background-color: white;
          width: 100%;
        }

        thead tr th {
          width: 100%;
        }
        tbody tr td {
          &:nth-child(1),
          &:nth-child(2) {
            max-width: 14%;
          }
          &:nth-child(3),
          &:nth-child(4) {
            max-width: 5%;
          }
          &:nth-child(5),
          &:nth-child(6) {
            max-width: 8%;
          }
          &:nth-child(7) {
            padding-left: 8px;
            padding-right: 8px;
            max-width: 20%;
          }

          &:nth-child(9) {
            max-width: 36%;
          }
        }
        thead tr th {
          &:nth-child(1),
          &:nth-child(2) {
            max-width: 14%;
          }
          &:nth-child(3),
          &:nth-child(4) {
            max-width: 5%;
          }
          &:nth-child(5),
          &:nth-child(6) {
            max-width: 8%;
          }
          &:nth-child(7) {
            max-width: 36%;
          }

          &:nth-child(9) {
            max-width: 20%;
          }
        }
      }
    }
    .bx--pagination {
      background-color: white;
      .bx--select-input {
        background-color: white;
      }
    }
  }

  .bx--toolbar-content {
    .custom-button-download {
      padding: 0 16px;
      @extend .ocn--h-40;
    }
  }
  .bx--table-toolbar {
    min-height: 100%;
    .bx--toolbar-content {
      height: 100%;
    }
  }
  .loading-booking_ocn {
    height: calc(100vh - 540px);
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .loading-icon {
      transform: scale(0.8);
    }
  }
  @mixin css_min_screen {
    .form-wrapper {
      .input-wrapper {
        max-width: unset !important;
        width: unset;
        flex-grow: 1;
      }
      .form-action {
        flex-direction: row-reverse;
        justify-content: unset;
        width: 100%;
        flex-grow: unset;
        margin-top: 0px;
        .action__btn + .action__btn {
          margin-top: 0;
          margin-right: 4px;
        }
      }
    }
  }

  @include minScreen {
    @include css_min_screen;
  }

  @include endpoint_1446 {
    @include css_min_screen;
  }
}
