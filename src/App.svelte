<script>
  import { cloneDeep } from 'lodash';
  import Router, { push } from 'svelte-spa-router';
  import { wrap } from 'svelte-spa-router/wrap.js';
  import { setupI18n, isLocaleLoaded, dir } from './lib/i18n.js';
  import { ReservationManagementReport, NotFound, SignIn, ResetPassword } from 'src/pages';
  import { instanceAxios } from './service/request.js';
  import { location } from 'svelte-spa-router';
  import BotSupport from 'src/components/bot-support/index.svelte';
  import { user, screenIsNotEvent } from 'src/store';
  import 'carbon-components-svelte/css/all.css';
  import 'src/styles/app.scss';

  const ROUTES = [
    {
      key: '/',
      component: ReservationManagementReport,
      isPrivate: true,
    },
    {
      key: '/sign-in',
      component: SignIn,
      isPrivate: false,
    },
    {
      key: '/reset-password',
      component: ResetPassword,
      isPrivate: false,
    },
    {
      key: '*',
      component: NotFound,
    },
  ];

  $: if (!$isLocaleLoaded) {
    // TODO set default lang - 'ko'
    setupI18n({ withLocale: 'ko' });
  }
  $: {
    document.dir = $dir;
  }

  function logOut() {
    user.set('LOGOUT');
    push('/sign-in');
  }

  function routeLoaded(event) {
    const publicRoutes = ROUTES.filter((route) => !route?.isPrivate && route?.isPrivate !== undefined).map(
      (route) => route.key
    );
    const route = event.detail.route;
    if (publicRoutes.includes(route)) return;
    const auth = cloneDeep($user);
    if (auth?.accessToken) {
      instanceAxios.defaults.headers.common.Authorization = `Bearer ${auth.accessToken}`;
      if (route === '/sign-in') {
        push('/');
      }
    } else {
      logOut();
    }
  }

  function generateRoute(baseRoutes) {
    const routes = {};

    for (let baseRoute of baseRoutes) {
      const { key, component, isPrivate } = baseRoute;
      routes[key] = wrap({
        component,
        ...(!isPrivate &&
          isPrivate !== undefined && {
            conditions: [
              async (_) => {
                const auth = cloneDeep($user);
                const isLogin = auth?.accessToken;
                if (isLogin) {
                  push('/');
                  return false;
                }
                return true;
              },
            ],
          }),
      });
    }

    return routes;
  }
</script>

{#if $isLocaleLoaded}
  {#if !$location.includes('sign-in')}
    <BotSupport />
  {/if}
  <Router routes={generateRoute(ROUTES)} on:routeLoaded={routeLoaded} />
{:else}
  <p>Loading...</p>
{/if}
{#if $screenIsNotEvent}
  <div class="g-screen-transparent-isnot-event" />
{/if}

<div id="box-modal">
  <div id="box-modal-children" />
</div>
