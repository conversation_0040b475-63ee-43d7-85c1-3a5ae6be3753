{"layout": {"gnb": {"searchPnr": {"placeHolder": "PNR 입력 후 엔터"}, "mgmtReservation": {"title": "예약관리", "subMenus": {"listReservation": "예약리스트", "listOcn": "OCN 리스트", "reservationManagement": "예약관리", "listDsr": "DSR 리스트", "reservationTicketing": "예약/발권", "listUser": "User List"}}}, "pages": {"reservationManagement": {"ocnTable": {"toolbar": {"titleTable": "OCN 정보", "buttonAgreeOcn": "OCN 동의"}, "dataTable": {"headers": {"receiveAirline": "항공사 OCN 수신일시", "travelAgencyReception": "여행사 OCN 수신일시", "airline": "항공사", "type": "OCN유형", "actionTypeOrContext": "OCN상세", "description": "Description"}}}, "itineraryInformationTable": {"toolbar": {"titleTable": "여정 정보", "buttonAgreeOcn": "여정 변경", "buttonService": "서비스", "buttonSeat": "좌석"}, "dataTable": {"headers": {"airlineId": "항공사", "flightNumber": "편명", "departureAirportCode": "출발지", "arrivalAirportCode": "도착지", "departureTime": "출발시간", "departureDate": "출발일", "arrivalDate": "도착일", "arrivalTime": "도착시간", "cabinCode": "좌석등급", "name": "PriceClass", "rbd": "RBD", "maxWeightAllowance": "수하물", "aircraftCode": "기종", "typeAirlineId": "코드쉐어", "seg": "숨은경유지", "status": "상태"}}, "itineraryInquiry": {"formChangeItinerary": {}}, "checkAndChangeItinerary": {"airlineId": "항공사", "flightNumber": "편명", "departureAirportCode": "출발지", "arrivalAirportCode": "도착지", "departureTime": "출발일시", "arrivalTime": "도착일시", "cabinCode": "좌석등급", "name": "priceClass", "rbd": "RBD", "maxWeightAllowance": "수하물", "typeAirlineId": "코드쉐어", "rule": "규정"}, "chooseAChangeItinerary": {"airlineId": "항공사", "flightNumber": "편명", "aircraftCode": "기종", "departureAirportCode": "출발지", "arrivalAirportCode": "도착지", "departureTime": "출발일시", "arrivalTime": "도착일시", "cabinCode": "좌석등급", "name": "priceClass", "rbd": "RBD", "maxWeightAllowance": "수하물", "typeAirlineId": "코드쉐어"}, "differenceInformationHeader": {"totalAmountOriginalOrderItem": "기존 항공권 총운임", "totalAmountNewOfferItem": "변경 항공권 총운임", "totalAmountPenalty": "예약 변경 패널티", "totalAmountReshopDue": "운임 차액", "totalAmountPrice": "최종 결제 금액"}}, "passengerInformationTable": {"toolbar": {"titleTable": "탑승객 정보", "buttonAgreeOcn": "탑승객 분리", "buttonPassengerInformationTable": "정보 수정"}, "dataTable": {"headers": {"id": "순번", "nameTitle": "TITLE", "givenNameAndPtc": "탑승객명(PTC)", "birthDate": "생년월일", "phoneNumber": "모바일", "emailAddress": "이메일", "identityDocument": "여권번호", "issuingCountryCode": "여권발행국", "expiryDate": "여권만료일", "citizenshipCountryCode": "국적", "countryCode": "거주국가", "surnAndGiven": "동반유아/성인", "ffn": "FFN"}}}, "reservationInformationTable": {"toolbar": {"titleTable": "예약 정보", "paymentOrIssue": "결제&발행"}, "content": {"orderStatus": "예약상태", "airlineID": "항공사", "createDate": "예약일시", "id": "PNR", "ticketingDate": "발권일시", "orderID": "OrderID", "paymentTimeLimit": "결제TL", "ticketTimeLimit": "발권TL", "priceGuaranteeTimeLimit": "요금보장TL"}}, "ssrTable": {"toolbar": {"titleTable": "SSR 정보"}, "dataTable": {"headers": {"airlineID": "항공사", "givenNameAndPtc": "탑승객명(PTC)", "name": "SSR", "status": "상태", "flightNumberAirportCodeDate": "Segments"}}}, "ticketInformationTable": {"toolbar": {"titleTable": "티켓 정보"}, "dataTable": {"headers": {"ticketDocNbr": "티켓번호", "type": "티켓유형", "passengerName": "탑승객명(PTC)", "ticketingDate": "발권일시", "flightNumberAirportCodeDates": "Segments", "edm": "EMD상세", "totalFareTicket": "Total Fare"}}, "popupHeader": {"passengerName": "탑승객명(PTC)", "ticketingDate": "발권일시", "id": "PNR", "reportingType": "정산타입", "ticketDocNbr": "티켓번호", "airlineID": "발권항공사", "orderID": "OrderID", "ticketStatus": "티켓상태"}, "popupEMDA": {"couponNumber": "순번", "airlineID": "항공사", "flightNumber": "편명", "departureAirportCode": "출발지", "arrivalAirportCode": "도착지", "departureDateTime": "출발일시", "emdDetails": "EMD상세", "relatedTickets": "관련티켓", "status": "쿠폰상태"}, "popupEMDS": {"couponNumber": "순번", "serviceCity": "서비스 도시", "code": "코드", "emdDetails": "서비스 상세", "relatedTickets": "관련티켓", "status": "쿠폰상태"}, "popupTicket": {"couponNumber": "순번", "airlineID": "항공사", "flightNumber": "편명", "departureAirportCode": "출발지", "arrivalAirportCode": "도착지", "departureDateTime": "출발일시", "arrivalDateTime": "도착일시", "cabinCode": "좌석등급", "name": "PriceClass", "fareBasisCode": "FareBasis", "baggage": "수하물", "rbd": "RBD", "codeShare": "코드쉐어", "aircraftCode": "기종", "status": "쿠폰상태"}}, "amountInformationTable": {"toolbar": {"titleTable": "금액 정보"}, "dataTable": {"headers": {"passengerNamePtc": "탑승객명 (PTC)", "item": "<PERSON><PERSON>", "segments": "Segments", "baseAmount": "BaseAmount", "totalTax": "TotalTax", "totalFare": "TotalFare", "actionTicket": ""}}}}, "listOcn": {"titleTable": "검색결과", "btnOcnManualSending": "OCN 수동발송", "btnDownload": "엑셀 다운로드", "btnGetData": "검색", "btnResetFilter": "초기화", "headerTable": {"airlineDateTime": "항공사 OCN 수신일시", "travelDateTime": "여행사 OCN 수신일시", "airline": "항공사", "typeOcn": "OCN유형", "pnr": "PNR", "otherPnr": "Other PNR", "orderID": "OrderID", "receiveOcn": "OCN수신여부"}}, "bookingContent": {"titleTable": "검색결과", "btnDownload": "엑셀 다운로드", "btnResetFilter": "초기화", "btnGetData": "검색", "headerTable": {"createDate": "예약일시", "ticketingDate": "발권일시", "orderStatus": "예약상태", "airlineID": "항공사", "bookingReference": "PNR", "otherPnr": "Other PNR", "OrderID": "OrderID", "surGiveName": "탑승객명", "itinerary": "여정요약", "departureDate": "출발일"}}, "dsrList": {"titleTable": "검색결과", "btnDownload": "엑셀 다운로드", "headerTable": {"releaseDateAndTime": "처리일시", "ticketType": "티켓유형", "airline": "항공사", "ticketNumber": "티켓번호", "passengerName": "탑승객명", "pnr": "PNR", "orderId": "OrderID", "totalFare": "Total Fare", "baseAmount": "Base Amount", "totalTax": "Total Tax", "rfndPenalty": "RFND Penalty", "currency": "<PERSON><PERSON><PERSON><PERSON>", "fop": "FOP", "fareBasis": "<PERSON><PERSON>", "emd": "EMD상세"}}}}}