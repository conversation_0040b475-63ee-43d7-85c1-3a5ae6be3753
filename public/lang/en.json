{"layout": {"gnb": {"search_pnr": {"place_holder": "PNR 6자리 입력 후 엔터xx"}, "mgmt_reservation": {"title": "Reservation", "subMenus": {"listReservation": "Reservation List", "listOcn": "OCN list", "reservationManagement": "Reservation Management", "listDsr": "DSR List", "reservationTicketing": "Reservation/Ticketing", "listUser": "User List"}}}, "header": {}, "footer": {}, "pages": {"reservationManagement": {"ocnTable": {"toolbar": {"titleTable": "", "buttonAgreeOcn": ""}, "dataTable": {"headers": {"receiveAirline": "Receive Airline OCN", "travelAgencyReception": "", "airline": "", "type": "", "actionTypeOrContext": "", "description": ""}}}, "itineraryInformationTable": {"toolbar": {"titleTable": "", "buttonAgreeOcn": "", "buttonService": "", "buttonSeat": ""}, "dataTable": {"headers": {"airlineId": "", "flightNumber": "", "departureAirportCode": "", "arrivalAirportCode": "", "departureTime": "", "arrivalTime": "", "cabinCode": "", "name": "", "rbd": "", "maxWeightAllowance": "", "typeAirlineId": "", "seg": "", "status": ""}}, "itineraryInquiry": {"formChangeItinerary": {}}, "checkAndChangeItinerary": {"airlineId": "", "flightNumber": "", "departureAirportCode": "", "arrivalAirportCode": "", "departureTime": "", "arrivalTime": "", "cabinCode": "", "name": "", "rbd": "", "maxWeightAllowance": "", "typeAirlineId": "", "rule": ""}, "chooseAChangeItinerary": {"airlineId": "", "flightNumber": "", "aircraftCode": "", "departureAirportCode": "", "arrivalAirportCode": "", "departureTime": "", "arrivalTime": "", "cabinCode": "", "name": "", "rbd": "", "maxWeightAllowance": "", "typeAirlineId": ""}, "differenceInformationHeader": {"totalAmountOriginalOrderItem": "", "totalAmountNewOfferItem": "", "totalAmountPenalty": "", "totalAmountReshopDue": "", "totalAmountPrice": ""}}, "passengerInformationTable": {"toolbar": {"titleTable": "", "buttonAgreeOcn": "", "buttonPassengerInformationTable": ""}, "dataTable": {"headers": {"id": "", "nameTitle": "", "givenNameAndPtc": "", "birthDate": "", "phoneNumber": "", "emailAddress": "", "identityDocument": "", "issuingCountryCode": "", "expiryDate": "", "citizenshipCountryCode": "", "countryCode": "", "surnAndGiven": "", "ffn": ""}}}, "reservationInformationTable": {"toolbar": {"titleTable": "", "paymentOrIssue": ""}, "content": {"orderStatus": "", "airlineID": "", "createDate": "", "id": "", "ticketingDate": "", "orderID": "", "paymentTimeLimit": "", "ticketTimeLimit": "", "priceGuaranteeTimeLimit": ""}}, "ssrTable": {"toolbar": {"titleTable": ""}, "dataTable": {"headers": {"airlineID": "", "givenNameAndPtc": "", "name": "", "status": "", "flightNumberAirportCodeDate": ""}}}, "ticketInformationTable": {"toolbar": {"titleTable": ""}, "dataTable": {"headers": {"ticketDocNbr": "", "type": "", "passengerName": "", "ticketingDate": "", "flightNumberAirportCodeDates": "", "edm": "", "totalFareTicket": ""}}, "popupHeader": {"passengerName": "", "ticketingDate": "", "id": "", "reportingType": "", "ticketDocNbr": "", "airlineID": "", "orderID": "", "ticketStatus": ""}, "popupEMDA": {"couponNumber": "", "airlineID": "", "flightNumber": "", "departureAirportCode": "", "arrivalAirportCode": "", "departureDateTime": "", "emdDetails": "", "relatedTickets": "", "status": ""}, "popupEMDS": {"couponNumber": "", "code": "", "emdDetails": "", "relatedTickets": "", "status": ""}, "popupTicket": {"couponNumber": "", "airlineID": "", "flightNumber": "", "departureAirportCode": "", "arrivalAirportCode": "", "departureDateTime": "", "arrivalDateTime": "", "cabinCode": "", "name": "", "fareBasisCode": "", "baggage": "", "rbd": "", "codeShare": "", "aircraftCode": "", "status": ""}}, "amountInformationTable": {"toolbar": {"titleTable": ""}, "dataTable": {"headers": {"passengerNamePtc": "", "item": "", "segments": "", "baseAmount": "", "totalTax": "", "totalFare": "", "actionTicket": ""}}}}, "listOcn": {"titleTable": "", "btnOcnManualSending": "", "btnDownload": "", "btnGetData": "", "btnResetFilter": "", "headerTable": {"airlineDateTime": "", "travelDateTime": "", "airline": "", "typeOcn": "", "otherPnr": "", "pnr": "", "orderID": "", "receiveOcn": ""}}, "bookingContent": {"titleTable": "", "btnDownload": "", "btnResetFilter": "", "btnGetData": "", "headerTable": {"createDate": "", "ticketingDate": "", "orderStatus": "", "airlineID": "", "bookingReference": "", "otherPnr": "", "OrderID": "", "surGiveName": "", "itinerary": "", "departureDate": ""}}, "dsrList": {"titleTable": "", "btnDownload": "", "headerTable": {"releaseDateAndTime": "", "ticketType": "", "airline": "", "ticketNumber": "", "passengerName": "", "pnr": "", "orderId": "", "totalFare": "", "baseAmount": "", "totalTax": "", "rfndPenalty": "", "currency": "", "fop": "", "fareBasis": "", "emd": ""}}}}}